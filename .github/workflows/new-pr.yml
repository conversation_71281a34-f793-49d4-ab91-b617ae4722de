name: New PR Notification

on:
  pull_request:
    types: [opened]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Extract PR info
        run: |
          echo "PR Number: ${{ github.event.pull_request.number }}"
          echo "PR Title: ${{ github.event.pull_request.title }}"
          echo "PR Author: ${{ github.event.pull_request.user.login }}"
          echo "PR URL: ${{ github.event.pull_request.html_url }}"
          echo "Source Branch: ${{ github.event.pull_request.head.ref }}"
          echo "Target Branch: ${{ github.event.pull_request.base.ref }}"

          # Zapisz informacje do zmiennych środowiskowych
          echo "PR_NUMBER=${{ github.event.pull_request.number }}" >> $GITHUB_ENV
          echo "PR_TITLE=${{ github.event.pull_request.title }}" >> $GITHUB_ENV
          echo "PR_AUTHOR=${{ github.event.pull_request.user.login }}" >> $GITHUB_ENV
          echo "PR_URL=${{ github.event.pull_request.html_url }}" >> $GITHUB_ENV
          echo "SOURCE_BRANCH=${{ github.event.pull_request.head.ref }}" >> $GITHUB_ENV
          echo "TARGET_BRANCH=${{ github.event.pull_request.base.ref }}" >> $GITHUB_ENV

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL_NEWPR }}
          SLACK_CHANNEL: '#new-pr'
          SLACK_COLOR: '#87CEEB'
          SLACK_TITLE: '🅿️ Nowy Pull Request'
          SLACK_MESSAGE: |
            PR <${{ env.PR_URL }}|#${{ env.PR_NUMBER }}: ${{ env.PR_TITLE }}>
            Autor: ${{ env.PR_AUTHOR }}
            Branch: (${{ env.TARGET_BRANCH }}) ⬅️ ${{ env.SOURCE_BRANCH }}
            Link: ${{ env.PR_URL }}
          SLACK_FOOTER: 'GitHub Actions'