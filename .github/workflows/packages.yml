# NUGET_TOKEN
# Dla tego workflow używamy tylko secrets.NUGET_TOKEN, kt<PERSON>ry jest automatycznie dostępny w GitHub Actions - trzeba go ustawiać w repozytorium i musi być aktywny.
#  Wymagane uprawnienia dla NUGET_TOKEN:
#    1. write:packages (najważniejsze)
#    Pozwala na publikowanie i aktualizowanie pakietów
#    Niezbędne do wykonania dotnet nuget push
#    2. read:packages
#    Pozwala na pobieranie i czytanie pakietów
#    Potrzebne do sprawdzania istniejących wersji i --skip-duplicate
#    3. delete:packages (opcjonalne)
#    Tylko jeśli chcesz móc usuwać pakiety
#    Zazwyczaj nie jest potrzebne dla standardowego workflow
#  Jak utworzyć token:
#    Idź do GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
#    Kliknij Generate new token (classic)
#  Wybierz uprawnienia:
#    ✅ write:packages
#    ✅ read:packages
#    Skopiuj token i dodaj go jako secret NUGET_TOKEN w ustawieniach repozytorium
name: Build and publish NuGet packages
on:
  workflow_dispatch:  # Pozwala na ręczne uruchomienie
  push:
    branches: [ "main", "develop" ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'

      - name: Add GitHub Packages Source
        run: |
          dotnet nuget remove source BlachdomPLUS || true # usunęcie source BachdomPLUS jeśli jest
        
          dotnet nuget add source --username ${{ github.actor }} --password ${{ secrets.NUGET_TOKEN }} --store-password-in-clear-text --name BlachdomPLUS "https://nuget.pkg.github.com/blachdomplus/index.json"
        
      - name: Build EMessa.Base
        run: dotnet build -c Release -p:TargetFramework=net8.0 src/EMessa.Base/EMessa.Base.csproj

      - name: Build EMessa.DAL
        run: dotnet build -c Release -p:TargetFramework=net8.0 src/EMessa.DAL/EMessa.DAL.csproj

      - name: Push EMessa.Base Package
        run: dotnet nuget push src/EMessa.Base/bin/Release/EMessa.Base.*.nupkg --source "BlachdomPLUS" --api-key ${{ secrets.NUGET_TOKEN }} --skip-duplicate

      - name: Push EMessa.DAL Package
        run: dotnet nuget push src/EMessa.DAL/bin/Release/EMessa.DAL.*.nupkg --source "BlachdomPLUS" --api-key ${{ secrets.NUGET_TOKEN }} --skip-duplicate
      
      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()  # Ta akcja uruchomi się tylko w przypadku niepowodzenia
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL_CICD }}
          SLACK_CHANNEL: #ci-cd
          SLACK_COLOR: danger
          SLACK_ICON: https://github.com/github.png?size=48
          SLACK_TITLE: '❌ Nuget push Failed!'
          SLACK_MESSAGE: 'Stworzenie pakietów dla ${{ github.repository }} nie powiodło się. Sprawdź logi, aby uzyskać więcej informacji.'
          SLACK_FOOTER: 'GitHub Actions'
