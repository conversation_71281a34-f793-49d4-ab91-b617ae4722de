name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_call:  # Pozwala na wywoływanie tego workflow z innego
    inputs:
      environment:
        required: false
        type: string
        default: 'test'
    outputs:
      test-result:
        description: "Test result"
        value: ${{ jobs.test.outputs.result }}

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 8.0.x

      - name: Restore dependencies
        run: dotnet restore

      - name: Build
        run: dotnet build --no-restore --configuration Release

      - name: Test GUS.BIR.ServiceTests
        env:
          GUS_API_KEY: ${{ secrets.GUS_API_KEY }}
        run: dotnet test tests/GUS.BIR.ServiceTests --no-build --configuration Release
        continue-on-error: true

      - name: Test EMessa7.Core.Tests
        run: dotnet test tests/EMessa7.Core.Tests --no-build --configuration Release --verbosity normal

      - name: Test ViesVatTests
        run: dotnet test tests/ViesVatTests --no-build --configuration Release --verbosity normal

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install npm dependencies
        run: bun install

      - name: Run TypeScript tests
        run: bun test:typescript
        
      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()  # Ta akcja uruchomi się tylko w przypadku niepowodzenia
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL_TESTS }}
          SLACK_CHANNEL: #tests
          SLACK_COLOR: danger
          SLACK_ICON: https://github.com/github.png?size=48
          SLACK_TITLE: '❌ Test Failed!'
          SLACK_MESSAGE: |
            'Test dla ${{ github.repository }} nie powiódł się. Sprawdź logi, aby uzyskać więcej informacji.'
            ${{ job.status != 'success' && 'Logs: https://github.com/blachdomplus/eMessa7/actions' || '' }}
          SLACK_FOOTER: 'GitHub Actions'