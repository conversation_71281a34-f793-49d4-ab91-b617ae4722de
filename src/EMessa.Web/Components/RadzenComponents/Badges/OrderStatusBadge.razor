@using EMessa.Base.Enums
@using EMessa.Web.Helpers
@using <PERSON><PERSON>zen
@using Radzen.Blazor

<RadzenBadge Variant="Variant.Filled"
             BadgeStyle="@BadgeStyle.Base"
             class="@ResolvedClass"
             Text="@ResolvedText" />
@code {
    [Parameter] public required OrderStatus Status { get; set; }
    [Parameter] public string? CssClass { get; set; }
    [Parameter] public string? AdditionalText { get; set; }

    private string ResolvedClass =>
        $"{OrderStatusExtensions.ColorCssClassBg(Status)} text-white {CssClass}";

    private string ResolvedText =>
        string.IsNullOrEmpty(AdditionalText)
            ? EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(Status)
            : $"{EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(Status)} {AdditionalText}";
}
