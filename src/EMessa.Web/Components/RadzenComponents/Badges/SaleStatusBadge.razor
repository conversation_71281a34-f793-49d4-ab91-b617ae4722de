@using EMessa.Web.Constants
@using EMessa.Web.Extensions.EnumBadges
@using EMessa.Web.Helpers
@using Messa.Core.BL.eMessa.Sales.ViewModels
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

<RadzenBadge Variant="Variant.Filled"
             BadgeStyle="@BadgeStyle.Base"
             class="@ResolvedClass"
             Text="@(EnumDisplayHelper.GetEnumTrDisplayName<SaleStatus>(Status))" />

@code {
    [Parameter] public required SaleStatus Status { get; set; }
    [Parameter] public string? CssClass { get; set; }

    private string ResolvedClass =>
        $"{SaleStatusExtensions.ColorCssClassBg(Status)} text-white {CssClass}";
}
