@using Ra<PERSON><PERSON>
@using Radzen.Blazor

<RadzenFormField Variant="@Variant" Text="@Label" class="@FormFieldClass">
    <ChildContent>
        @ChildContent
    </ChildContent>
</RadzenFormField>

@code {
    // RadzenFormField parameters
    [Parameter] public Variant Variant { get; set; } = Variant.Outlined;
    [Parameter] public string? Label { get; set; }
    [Parameter] public string FormFieldClass { get; set; } = "";
    
    // Content parameter
    [Parameter] public RenderFragment? ChildContent { get; set; }
}
