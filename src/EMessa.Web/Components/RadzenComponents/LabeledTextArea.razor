@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON><PERSON>.Blazor

<RadzenFormField Variant="@Variant" Text="@Label" class="@FormFieldClass">
    <ChildContent>
        <RadzenTextArea Value="@(ReadOnly ? StringOrDefault(Value, DefaultValue) : Value)"
                        ValueChanged="@ValueChanged"
                        ReadOnly="@ReadOnly"
                        Disabled="@Disabled"
                        Placeholder="@Placeholder"
                        Rows="@Rows"
                        MaxLength="@MaxLength"
                        class="@TextAreaClass" />
    </ChildContent>
</RadzenFormField>

@code {
    // RadzenFormField parameters
    [Parameter] public Variant Variant { get; set; } = Variant.Outlined;
    [Parameter] public string? Label { get; set; }
    [Parameter] public string FormFieldClass { get; set; } = "";

    // RadzenTextArea parameters
    [Parameter] public required string Value { get; set; }
    [Parameter] public EventCallback<string> ValueChanged { get; set; }
    [Parameter] public bool ReadOnly { get; set; }
    [Parameter] public bool Disabled { get; set; }
    [Parameter] public string? Placeholder { get; set; }
    [Parameter] public int Rows { get; set; } = 3;
    [Parameter] public int? MaxLength { get; set; }
    [Parameter] public string TextAreaClass { get; set; } = "";
    [Parameter] public string DefaultValue { get; set; } = "";

    private string StringOrDefault(string? text, string defaultText)
    {
        return !string.IsNullOrEmpty(text) ? text : defaultText;
    }
}
