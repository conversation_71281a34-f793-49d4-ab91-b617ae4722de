@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON>zen.Blazor

<RadzenFormField Variant="@Variant" Text="@Label" class="@FormFieldClass">
    <ChildContent>
        <RadzenTextBox Value="@(ReadOnly ? StringOrDefault(Value, DefaultValue) : Value)"
                       ValueChanged="@ValueChanged"
                       ReadOnly="@ReadOnly"
                       Disabled="@Disabled"
                       Placeholder="@Placeholder"
                       MaxLength="@MaxLength"
                       class="@TextBoxClass" />
    </ChildContent>
</RadzenFormField>

@code {
    // RadzenFormField parameters
    [Parameter] public Variant Variant { get; set; } = Variant.Outlined;
    [Parameter] public string? Label { get; set; }
    [Parameter] public string FormFieldClass { get; set; } = "";

    // RadzenTextBox parameters
    [Parameter] public required string Value { get; set; }
    [Parameter] public EventCallback<string> ValueChanged { get; set; }
    [Parameter] public bool ReadOnly { get; set; }
    [Parameter] public bool Disabled { get; set; }
    [Parameter] public string? Placeholder { get; set; }
    [Parameter] public int? MaxLength { get; set; }
    [Parameter] public string TextBoxClass { get; set; } = "";
    [Parameter] public string DefaultValue { get; set; } = "";

    private string StringOrDefault(string? text, string defaultText)
    {
        return !string.IsNullOrEmpty(text) ? text : defaultText;
    }
}
