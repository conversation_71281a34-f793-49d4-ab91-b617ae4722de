@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Core.Features.Orders.Queries.Common

<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
    <OrderStatusBadge Status="@Order.Status" CssClass="text-base" />
    <RadzenText TextStyle="TextStyle.H6" class="m-0">
        @("Zamówienie".Tr())
        @if (!string.IsNullOrWhiteSpace(Order.No))
        {
            @(" ")
            <strong>@Order.No</strong>
        }
        @if (!string.IsNullOrWhiteSpace(Order.CustomerNo))
        {
            @(" ")
            <text> (@Order.CustomerNo)</text>
        }
    </RadzenText>
</RadzenStack>

@code {
    [Parameter] public required OrderResponse Order { get; set; }
}
