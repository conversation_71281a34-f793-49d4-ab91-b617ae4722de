@page "/orders/details/{OrderId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production},{Role.ClientManager},{Role.Client}")]

@using EMessa.Core.Features.OrderItems.Commands.DeleteOrderItem
@using EMessa.Core.Features.OrderItems.Commands.UpdateOrderItem
@using EMessa.Core.Features.OrderItems.Queries.GetOrderItemQuery
@using EMessa.Core.Features.Orders.Commands.AcceptOrder
@* @using EMessa.Core.Features.Orders.Commands.DeleteOrder *@
@using EMessa.Core.Features.Orders.Commands.RejectOrder
@using EMessa.Core.Features.Orders.Commands.SendOrder
@using EMessa.Core.Features.Orders.Queries.Common
@using EMessa.Core.Features.Orders.Queries.GetOrderById
@using EMessa.Core.Features.Orders.Commands.EditOrder
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Services
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Pages.Orders.Parts
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject DialogService DialogService
@inject ILockOrderService LockOrderService
@inject NotificationService NotificationService
@inject IOrderWatchService OrderWatchService
@inject ILogger<Details> Logger
@implements IDisposable

<IndexCard Title="@Title">

    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

    <RadzenCard class="px-0.5 py-1 !rounded-none">
        <RadzenStack Orientation="Orientation.Vertical" Gap=".5rem">

            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start"
                         Wrap="FlexWrap.Wrap">
                <BackRadzenButton Click="@GoBack" />

                @if (OrderAccessConstants.AllowedStatusesForEdit.Contains(_order.Status))
                {
                    const string iconClass = "[&_.rz-button-box]:text-black";
                    <EditRadzenButton Icon="@(!IsLockedByOtherUserOrOtherOrder ? "edit" : "lock")"
                                      Text="@(_isLockLoading ? "Edytuj" : IsLocked ? LockOrder?.OrderNo : "Edytuj".Tr())"
                                      Class="@string.Concat(
                                                 "bg-esa-edit",
                                                 IsLocked ? $" {iconClass}" : "",
                                                 IsLockedByOtherUserOrOtherOrder ? " opacity-70" : ""
                                             )"
                                      MouseEnter="@(er => OnLockedMouseEnter(er))"
                                      Click="@TryToggleEditOrder" />
                }

                <AuthorizeView Roles=@($"{Role.Administrator},{Role.TradeManager},{Role.Trade}")>
                    <Authorized>
                        @if (OrderAccessConstants.AllowedStatusesForSend.Contains(_order.Status))
                        {
                            <SaveRadzenButton Text="@("Wyślij do realizacji".Tr())"
                                              Icon="send"
                                              Click="@SendOrder" />
                        }
                        <AuthorizeView Roles=@($"{Role.Administrator},{Role.TradeManager},{Role.Trade}")>
                            <Authorized Context="acceptContext">
                                @if (OrderAccessConstants.AllowedStatusesForAccept.Contains(_order.Status))
                                {
                                    <SaveRadzenButton Text="@("Zatwierdź".Tr())"
                                                      Icon="check_circle"
                                                      Click="@AcceptOrder" />
                                }
                            </Authorized>
                        </AuthorizeView>
                        @if (OrderAccessConstants.AllowedStatusesForReject.Contains(_order.Status))
                        {
                            <EditRadzenButton Text="@("Wycofaj".Tr())"
                                              Icon="undo"
                                              Click="@RejectOrder" />
                        }
                        @* Andrzej prosił o usunięcie Delete bo rodzi to potem problemy *@
                        @* @if (OrderAccessConstants.AllowedStatusesForDelete.Contains(_order.Status)) *@
                        @* { *@
                        @*     <DeleteRadzenButton Text="@("Usuń".Tr())" *@
                        @*                         Click="@DeleteOrder" /> *@
                        @* } *@
                    </Authorized>
                </AuthorizeView>
            </RadzenStack>

            @switch (_viewMode)
            {
                default:
                case ViewMode.View:
                    <ViewOrder Order="@_order" />
                    break;
                case ViewMode.Edit:
                    <EditOrder Order="@_order"
                               EditOrderItem="@OnEditOrderItem"
                               DeleteOrderItem="@OnDeleteOrderItem"
                               Save="@OnSaveOrder"
                               Close="@OnCloseEditMode" />
                    break;
                case ViewMode.EditOrderItem:
                    if (_editingOrderItem != null)
                    {
                        <EditComplexOrderItem EditingItem="@_editingOrderItem"
                                              NewRequestedSheet="@_newRequestedSheet"
                                              OnSave="@OnSaveEditOrderItem"
                                              OnCancel="@OnCancelEditOrderItem" />
                    }

                    break;
            }
        </RadzenStack>
    </RadzenCard>

</IndexCard>

@code {
    [Parameter] public required int OrderId { get; set; }

    enum ViewMode
    {
        View,
        Edit,
        EditOrderItem
    }

    private ViewMode _viewMode = ViewMode.View;
    private GetOrderByIdResponse _order = new();
    private int _previousOrderId;
    private bool _isLoading;
    private bool _isLockLoading = true;
    private LockOrderResponse? _lockOrder;
    private bool IsLocked => _isLockLoading || LockOrder != null;

    private bool IsLockedByOtherUserOrOtherOrder => _isLockLoading || (LockOrder != null && (LockOrder.IsLockedByOtherUser || LockOrder.OrderId != OrderId));
    
    private LockOrderResponse? LockOrder
    {
        get => _lockOrder;
        set
        {
            _viewMode = value?.IsLockedByCurrentUser == true ? ViewMode.Edit : ViewMode.View;
            _lockOrder = value;
            _isLockLoading = false;
        }
    }

    private string Title => _viewMode switch
    {
        ViewMode.View => "Podgląd zamówienia".Tr(),
        ViewMode.Edit => "Edycja zamówienia".Tr(),
        ViewMode.EditOrderItem => "Edycja pozycji zamówienia".Tr(),
        _ => ""
    } +  $" ({_order.No})";

    protected override void OnInitialized()
    {
        base.OnInitialized();
        AppStateService.StateChanged += OnStateChanged;
        OrderWatchService.Subscribe(this, OrderId, OnOrderChanged);
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
        OrderWatchService.Unsubscribe(this, OrderId);
    }

    private async Task OnOrderChanged(int orderId)
    {
        await GetOrderLockAsync();
        await InvokeAsync(StateHasChanged);
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source == this) return;
        switch (property)
        {
            case AppStateNotifyProperties.ScreenSizeChanged:
                InvokeAsync(StateHasChanged);
                break;
            case AppStateNotifyProperties.SetUserData:
                // Można pobrać lockOrder dopiero mając UserProfileId
                _ = GetOrderLockAsync();
                break;
            case AppStateNotifyProperties.OwnLockedOrderChanged:
                // Zmieniono własne zamówienie
                if (LockOrder == null)
                {
                    LockOrder = AppStateService.OwnLockedOrder;
                    InvokeAsync(StateHasChanged);
                }

                break;
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            await GetOrderAsync();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        // Mechanizm przeładowania danych kiedy zmienia się path np:
        // /orders/details/111 -> /orders/details/222
        // Bez tego strona nadal prezentuje dane z /111
        if (OrderId != _previousOrderId)
        {
            _previousOrderId = OrderId;
            await GetOrderAsync();
            StateHasChanged();
        }

        await GetOrderLockAsync();
    }

    private async Task GetOrderLockAsync()
    {
        var userProfileId = AppStateService.GetUserProfileId();

        // Jeśli jest własne zamówienie to ustawiamy je
        if (AppStateService.OwnLockedOrder != null)
        {
            LockOrder = AppStateService.OwnLockedOrder;
            await InvokeAsync(StateHasChanged);
            return;
        }

        if (OrderId == 0 || userProfileId == 0)
            return;

        var response = await LockOrderService.GetOrderLockAsync(OrderId, userProfileId);
        if (response.Succeeded)
        {
            LockOrder = response.Data;
            await InvokeAsync(StateHasChanged);
            AppStateService.SetOwnLockedOrder(this, response.Data);
        }
        else
        {
            NotificationService.Notify(NotificationSeverity.Error, "Błąd", string.Join(", ", response.Messages));
        }
    }

    private async Task GetOrderAsync()
    {
        _isLoading = true;

        if (OrderId > 0 && AppStateService.GetUserProfileId() > 0)
        {
            var response = await Mediator.Send(new GetOrderByIdQuery(OrderId));

            if (response.Succeeded)
            {
                _order = response.Data;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, response.Messages);
                Navigation.NavigateTo("/orders/index");
            }
        }

        _isLoading = false;

        await InvokeAsync(StateHasChanged);
    }

    private void GoBack() => Navigation.NavigateTo("/orders/index");

    private async Task SendOrder() =>
        await ChangeStatusAsync(
            new SendOrderCommand(OrderId),
            "Zamówienie zostało wysłane do realizacji.".Tr(),
            () => ModalDialogService.Confirmation("Czy na pewno chcesz wysłać zamówienie {0} do realizacji?".Tr(_order.No)));

    private async Task AcceptOrder()
    {
        int? factoryId = await DialogService.OpenAsync<SelectFactoryOnAcceptOrderDialog>(
            title: "Wybierz zakład".Tr(),
            parameters: new Dictionary<string, object>
            {
                { "OrderNo", _order.No },
                { "CustomerId", _order.CustomerId }
            },
            options: new DialogOptions()
            {
                Resizable = true,
                Draggable = true,
                Width = "520px",
                Height = "auto"
            });

        if (!factoryId.HasValue)
            return;

        await ChangeStatusAsync(
            new AcceptOrderCommand(OrderId, factoryId.Value),
            "Zamówienie zostało zatwierdzone.".Tr());
    }

    private async Task RejectOrder() =>
        await ChangeStatusAsync(
            new RejectOrderCommand(OrderId),
            "Zamówienie zostało wycofane.".Tr(),
            () => ModalDialogService.Confirmation("Czy na pewno chcesz wycofać zamówienie {0}?".Tr(_order.No)));

    // Wyłączone na prośbę Andrzeja (usuwanie było problematyczne)
    // private async Task DeleteOrder() =>
    //    await ChangeStatusAsync(
    //        new DeleteOrderCommand(OrderId),
    //        "Zamówienie zostało usunięte.".Tr(),
    //       () => ModalDialogService.DeleteConfirmationByName(_order.No));

    private async Task ChangeStatusAsync(
        IRequest<IResult> request,
        string successMessage,
        Func<Task<bool>>? confirmation = null)
    {
        if (confirmation != null)
        {
            var confirmed = await confirmation.Invoke();
            if (!confirmed)
                return;
        }

        _isLoading = true;

        if (OrderId > 0)
        {
            var response = await Mediator.Send(request);

            if (response.Succeeded)
            {
                await GetOrderAsync();
                ToastService.Show(ToastType.SaveSuccess, successMessage);
            }
            else
            {
                ToastService.Show(ToastType.SaveError, response.Messages);
            }
        }

        _isLoading = false;
    }

    private async Task TryToggleEditOrder()
    {
        var userId = AppStateService.GetUserProfileId();

        var response = LockOrder is null
            ? await LockOrderService.TryLockOrderAsync(OrderId, userId)
            : await LockOrderService.TryUnlockOrderLockAsync(OrderId, userId);

        if (response.Succeeded)
        {
            LockOrder = response.Data;
            AppStateService.SetOwnLockedOrder(this, LockOrder);
        }
        else
        {
            ToastService.Show(ToastType.LockOrderError, response.Messages);
        }
    }

    private void OnLockedMouseEnter(ElementReference er)
    {
        if (LockOrder == null) return;
        if (LockOrder.IsLockedByOtherUser)
        {
            EmessaTooltipService.Open(er, "Edytowane przez".Tr().AddText($" ({LockOrder!.LockedBy})"));
        }
        else if (LockOrder.OrderId != OrderId)
        {
            EmessaTooltipService.Open(er, "Edytujesz inne zamówienie".Tr().AddText($" ({LockOrder.OrderNo})"));
        }
    }

    #region Order

    private async Task TryUnlockOrder()
    {
        var response = await LockOrderService.TryUnlockOrderLockAsync(OrderId, AppStateService.GetUserProfileId());
        if (response.Succeeded)
        {
            LockOrder = response.Data;
            AppStateService.SetOwnLockedOrder(this, LockOrder);
        }
        else
        {
            ToastService.Show(ToastType.LockOrderError, response.Messages);
        }
    }

    private async Task UpdateOrder()
    {
        if (_order.Id > 0)
        {
            var request = Mapper.Map<EditOrderRequest>(_order);
            var response = await Mediator.Send(new EditOrderCommand(request));

            if (response.Succeeded)
            {
                ToastService.ShowSuccess("Zamówienie zostało pomyślnie zaktualizowany.".Tr());
            }
            else
            {
                ToastService.Show(ToastType.SaveError, response.Messages);
            }
        }
    }

    private async Task OnSaveOrder()
    {
        await UpdateOrder();
        await TryUnlockOrder();
    }

    private async Task OnCloseEditMode()
    {
        await TryUnlockOrder();
        await GetOrderAsync();
    }

    #endregion

    #region OrderItem

    private BaseOrderItemEditModel? _editingOrderItem;
    private RequestedSheetEditModel? _newRequestedSheet;

    private async Task<BaseOrderItemEditModel?> LoadEditingItem(int orderItemId)
    {
        _isLoading = true;
        
        var response = await Mediator.Send(new GetOrderItemQuery(orderItemId));
        
        _isLoading = false;
        
        if (response.Succeeded)
        {
            return response.Data;
        }

        ToastService.Show(ToastType.LoadDataError, response.Messages);
        return null;
    }

    private async Task OnEditOrderItem(int orderItemId)
    {
        _viewMode = ViewMode.EditOrderItem;
        _editingOrderItem = await LoadEditingItem(orderItemId);
        if (_editingOrderItem == null)
        {
            _viewMode = ViewMode.Edit;
            return;
        }

        PresetNewRequestedSheet(_editingOrderItem.Article);
        StateHasChanged();
    }

    private async Task OnDeleteOrderItem(int orderItemId)
    {
        var item = _order.OrderItems.FirstOrDefault(x => x.Id == orderItemId);
        if (item == null)
            return;
        
        var confirmed = await ModalDialogService.DeleteConfirmationByName(item.ArticleName);
        if (confirmed)
        {
            var response = await Mediator.Send(new DeleteOrderItem(_order.Id, orderItemId));
            if (response.Succeeded)
            {
                ToastService.Show(ToastType.DeleteSuccess);
                await GetOrderAsync();
            }
            else
            {
                ToastService.Show(ToastType.DeleteError, response.Messages);
            }
        }
    }
    
    private async Task UpdateOrderItem(BaseOrderItemEditModel? updateOrderItem)
    {
        if (updateOrderItem == null)
        {
            Logger.LogWarning("Updating order item is null.");
            ToastService.Show(ToastType.UpdateError, "Nie można zaktualizować́ pustego artykułu.".Tr());
            return;
        }
        
        var updatedItem = Mapper.Map<UpdateOrderItemEditModel>(updateOrderItem);
        
        var result = await Mediator.Send(new UpdateOrderItemCommand(OrderId, updatedItem));
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.UpdateSuccess);
        }
        else
        {
            ToastService.Show(ToastType.UpdateError, result.Messages.String());
        }
    }

    private async Task OnSaveEditOrderItem()
    {
        await UpdateOrderItem(_editingOrderItem);
        _editingOrderItem = null;
        _viewMode = ViewMode.Edit;
        await GetOrderAsync();
    }

    private void OnCancelEditOrderItem()
    {
        _editingOrderItem = null;
        _newRequestedSheet = null;
        _viewMode = ViewMode.Edit;
    }

    private void PresetNewRequestedSheet(OrderItemEditArticleModel article)
    {
        _newRequestedSheet = new RequestedSheetEditModel
        {
            Quantity = article.DefaultQuantity,
            Length = article.LengthEditable ? 0m : article.DefaultLength,
            Width = article.WidthEditable ? 0m : article.DefaultWidth
        };
    }

    #endregion

}
