
<div class="flex flex-1 flex-col bg-gray-100 sm:rounded-md border @Class">
    @if (ShouldRenderHeader)
    {
        <div class="flex flex-row px-1 py-0.5 border-b border-black border-opacity-10 @JustifyHeader">
            @if (IsTitle)
            {
                <div class="flex flex-row justify-start gap-x-1">
                    <h1 class="text-xl font-semibold whitespace-normal sm:whitespace-nowrap">
                        @Title
                    </h1>
                </div>
            }
            @if (IsHeaderContent)
            {
                <div class="flex flex-row justify-end gap-x-1">
                    @HeaderContent
                </div>
            }
        </div>
    }
    @if (!ErrorMessage.IsEmpty())
    {
        <div class="m-3 p-2 text-red-500 border-1 border-danger-500 bg-danger-100">
            @ErrorMessage
        </div>
    }
    <div class="flex flex-col w-full @ChildClass">
        @ChildContent
    </div>
</div>

@code {

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter] public string Class { get; set; } = "";

    [Parameter]
    public string ChildClass { get; set; } = "";

    [Parameter] public RenderFragment? HeaderContent { get; set; }

    [Parameter] public string ErrorMessage { get; set; } = "";

    [Parameter] public RenderFragment? ChildContent { get; set; }

    private bool IsTitle => !string.IsNullOrEmpty(Title);
    private bool IsHeaderContent => HeaderContent != null;

    private bool ShouldRenderHeader => IsTitle || IsHeaderContent;

    private string JustifyHeader => IsTitle && IsHeaderContent
        ? "justify-between"
        : IsHeaderContent
            ? "justify-end"
            : "justify-start";
}