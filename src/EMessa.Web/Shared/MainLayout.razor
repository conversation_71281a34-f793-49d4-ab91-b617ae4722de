@using EMessa.Core.Features.Profiles.Queries.Get;
@using EMessa.Core.Features.Users.Queries.AppStateData;
@using Radzen.Blazor
@using EMessa.Core.Helpers
@using EMessa.Base.Enums
@using EMessa.Core.Features.Countries.Commands.SetCountriesConstans
@using EMessa.Core.Features.Notifications.Queries.NextUserNotification
@using Radzen
@using System.Reflection

@inherits LayoutComponentBase
@inject IJSRuntime JsRuntime
@inject NavigationManager NavigationManager
@inject TooltipService TooltipService
@implements IDisposable

<PageTitle>@_siteName</PageTitle>

<style>
    .rz-header {
        border: none;
    }

    .rz-body {
        padding: 0 !important;
    }

    .rz-sidebar {
        border-inline-end: none;
        scrollbar-width: none;
    }

    .rz-sidebar.sidebar-close {
        width: 50px !important;
    }

    .rz-sidebar.sidebar-close li {
        padding: 0 0 !important;
    }

    .rz-sidebar.sidebar-open a.nav-link {
        padding: 8px 10px;
    }

    .rz-sidebar.sidebar-close a.nav-link {
        padding: 6px 10px;
    }

    .rz-sidebar .nav-link {
        display: flex;
        align-items: center;
        color: black;
        height: 2.5rem;
    }

    .nav-item a {
        color: #d7d7d7;
        border-radius: 4px;
    }

    .nav-item a.active {
        background-color: #D1D5DB;
    }

    .nav-item a:hover {
        background-color: #F3F4F6;
    }

    .rz-sidebar.sidebar-close .nav-link {
        justify-content: center;
    }

    .rz-sidebar.sidebar-open .nav-link .displayText {
        font-size: 16px;
    }

    .sidebar-close .displayText {
        display: none;
    }
</style>

@* //TODO: material material-dark, standard ...,  *@
@* <RadzenTheme Theme="material" /> *@
<RadzenLayout>
    <RadzenHeader>
        <RadzenStack class="bg-gray-100" Orientation="Radzen.Orientation.Horizontal"
                     JustifyContent="Radzen.JustifyContent.SpaceBetween" AlignItems="Radzen.AlignItems.Center" Gap="0">
            @if (!AppStateService.IsLargeScreen)
            {
                <div class="flex items-center justify-content-center h-[50px]">
                    <RadzenButton class="p-1 px-2.5 ml-3 text-white bg-red-500! hover:bg-red-600 active:bg-red"
                                  Style="background-color: #E30010;"
                                  Icon="menu"
                                  Click="@ToggleNavMenu" />
                </div>
            }
            else
            {
                <div class="flex flex-row bg-red text-white w-[260px]">
                    <RadzenSidebarToggle class="m-0 border-none text-white bg-red hover:bg-red-600 active:bg-red"
                                         Click="@ToggleNavMenu" />
                    <div class="navbar-brand displayText px-3 m-0 flex flex-grow items-center justify-content-center">
                        <a href="#">
                            <span class="text-xl text-slate-100 mr-0">@_siteName</span>
                        </a>
                    </div>
                </div>
            }

            <div class="flex flex-row !gap-1 md:!gap-3 mx-1 md:mr-4">
                <LockedOrder />
                <ShoppingCartComponent />
                <CultureSwitcher />
                <LoginDisplay />
            </div>
        </RadzenStack>
    </RadzenHeader>
    @if (!AppStateService.IsLargeScreen)
    {
        <RadzenSidebar Responsive="false"
                       @bind-Expanded="@ShowSidebar"
                       @onclick="@(() => ShowSidebar = false)"
                       Style="width: 100vw; margin-top: 50px; position: absolute; background: none">
            <div class="w-[260px] bg-gray-200">
                <div class="flex items-center justify-content-center bg-red text-white h-[40px]">
                    <a href="#">
                        <span class="text-xl text-slate-100 mr-0">@_siteName</span>
                    </a>
                </div>
                <NavMenu />
            </div>
        </RadzenSidebar>
    }
    else
    {
        <RadzenSidebar Responsive="false"
                       class=@string.Concat("w-[260px] t-0 b-0 bg-gray-200 z-[1001] ", ShowSidebar ? "sidebar-open" : "sidebar-close")>
            <div class="pt-2">
                <NavMenu />
            </div>
        </RadzenSidebar>
    }
    <RadzenBody>
        <div class="flex flex-col" style="@string.Concat("height: calc(100vh - 50px);")">
            <div class="flex-grow max-sm:p-0 sm:p-1">
                <CascadingValue Value="@_toast">
                    @Body
                </CascadingValue>
            </div>

            <div
                class="flex flex-row gap-x-6 w-full p-2 text-xs text-gray-600 justify-end items-center h-10 bg-gray-100 border-t border-gray-300">
                @if (AppStateService.UserData.HasAdminRole)
                {
                    <div>
                        Lokalizacja: @System.Globalization.CultureInfo.CurrentCulture.DisplayName (@System.Globalization.CultureInfo.CurrentCulture.Name)
                    </div>
                    <div>Język: @_browserLanguage</div>
                    <div>Szerokość: @AppStateService.AppScreenSize (@(AppStateService.ScreenWidth)px)</div>
                    <div title="Ostatnia zmiana">@_gitRevisionShort/@_gitDateShort</div>
                }
                <RadzenText class="m-0 text-xs"
                            MouseEnter="@(er => TooltipService.Open(er, $"Ostatnia zmiana {_gitDateShort}. Git rev. {_gitRevisionShort}."))">
                    eMessa &copy; <EMAIL> v.@_version
                </RadzenText>
            </div>
        </div>
    </RadzenBody>
</RadzenLayout>

<AppToast @ref="_toast" />

<RadzenDialog />
<RadzenNotification
    Style="position: absolute; top: 0; left: 50%; transform: translate(-50%, 0%); inset-inline-end: unset;" />
<RadzenTooltip />
<RadzenContextMenu />

@code
{
    private AppToast _toast = new(); //TODO: CascadingValue

    private const string GitRevisionFile = "git-rev-info.txt";
    private string _gitRevisionShort = "";
    private string _gitDateShort = "";

    private string _version = "";

    private string _browserLanguage = "-";
    private readonly string _siteName = ConfigurationHelper.GetConfiguration<string>(ConfigurationCodes.SiteName) ?? string.Empty;

    public bool ShowSidebar;

    private static string ReadFile(string path)
    {
        if (File.Exists(path))
        {
            return File.ReadAllText(path).Trim();
        }

        path = Path.GetFullPath($"../../{path}");

        if (File.Exists(path))
        {
            return File.ReadAllText(path).Trim();
        }

        return "Brak".Tr();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
        
        var asm = typeof(Program).Assembly;
        var raw =
            (asm.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ??
             asm.GetCustomAttribute<AssemblyFileVersionAttribute>()?.Version ??
             asm.GetName().Version?.ToString() ??
             "Brak");
        var parts = raw.Split('+', 2);
        var meta = parts.Length == 2 ? parts[1] : null;
        var meta3 = meta is { Length: > 3 } ? meta[..3] : meta;
        _version = meta3 is null ? parts[0] : $"{parts[0]}+{meta3}";

        var gitRevisionSplit = ReadFile(GitRevisionFile).Split('|');
        _gitRevisionShort = gitRevisionSplit.Length > 0 ? gitRevisionSplit[0] : "Brak";
        if (gitRevisionSplit.Length > 1 && gitRevisionSplit[1].Length >= 19)
        {
            _gitDateShort = gitRevisionSplit[1].Substring(0, 19); // pierwsze 19 znaków
        }
        else
        {
            _gitDateShort = "Brak";
        }


        await Mediator.Send(new SetCountriesConstansCommand());

        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var authStateUser = authState.User;
        if (authStateUser.Identity is not { IsAuthenticated: true })
        {
            NavigationManager.NavigateTo("Account/Login");
        }
        else
        {
            var userName = authStateUser.GetUserName();
            var userProfile = await Mediator.Send(new GetProfileQuery(userName));
            if (userProfile.Succeeded)
            {
                var userDataResult = await Mediator.Send(new GetUserDataQuery(userName));
                if (userDataResult.Succeeded)
                {
                    var user = userDataResult.Data;
                    AppStateService.SetLoggedUser(this, 
                        user.UserProfileId,
                        user.FirstName, 
                        user.LastName, 
                        user.FirstConfiguration,
                        user.CustomerId, 
                        user.Roles, 
                        user.BranchIds, 
                        user.FactoryId,
                        user.Email
                        );
                    //AppStateService.ShoppingCartChanged(this);
                }
            }

            //     var nextUserNotification = await Mediator.Send(new NextUserNotificationQuery(userName));
            //     if (nextUserNotification is { Succeeded: true, Data.Id: > 0 })
            //     {
            //         NavigationManager.NavigateTo("/usermessages");
            //     }
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (property != AppStateNotifyProperties.ScreenSizeChanged) return;

        if (AppStateService.IsLargeScreen)
            ShowSidebar = true;
        else
            ShowSidebar = false;

        _ = InvokeAsync(StateHasChanged);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        _browserLanguage = await JsRuntime.InvokeAsync<string>("EMessa.getBrowserLanguage");
        if (firstRender)
        {
            var dotNetReference = DotNetObjectReference.Create(this);
            await JsRuntime.InvokeVoidAsync("EMessa.registerBlazorPage", dotNetReference);
            await JsRuntime.InvokeVoidAsync("EMessa.forceResize");

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var authStateUser = authState.User;
            if (authStateUser.Identity is not { IsAuthenticated: true })
            {
                NavigationManager.NavigateTo("Account/Login", true);
            }
            else
            {
                var userName = authStateUser.GetUserName();
                var userProfile = await Mediator.Send(new GetProfileQuery(userName));
                if (userProfile.Succeeded)
                {
                    var userDataResult = await Mediator.Send(new GetUserDataQuery(userName));
                    if (userDataResult.Succeeded)
                    {
                        var user = userDataResult.Data;
                        AppStateService.SetLoggedUser(
                            this, 
                            user.UserProfileId,
                            user.FirstName, 
                            user.LastName, 
                            user.FirstConfiguration,
                            user.CustomerId, 
                            user.Roles, 
                            user.BranchIds, 
                            user.FactoryId,
                            user.Email);
                        //AppStateService.ShoppingCartChanged(this);
                    }
                }

                var nextUserNotification = await Mediator.Send(new NextUserNotificationQuery(userName));
                if (nextUserNotification is { Succeeded: true, Data.Id: > 0 })
                {
                    NavigationManager.NavigateTo("/usermessages");
                }
            }
        }
    }

    public void ToggleNavMenu()
    {
        ShowSidebar = !ShowSidebar;
    }

    [JSInvokable]
    public void WindowResized(int width, int height)
    {
        AppStateService.SetScreenSize(this, width, height);
    }
}