@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using EMessa.Core.Features.Orders.Queries.Common
@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@using EMessa.Web.Pages.Orders.Parts

<div class="@Class">
    <RadzenStack Orientation="Orientation.Vertical" Gap=".5rem" class="p-0">
        <OrderHeader Order="@Order" />

        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start"
                     class="mt-1">
            <LabeledTextBox Label="@("Klient".Tr())"
                            Value="@Order.CustomerShortName"
                            DefaultValue=" "
                            TextBoxClass="w-full"
                            ReadOnly />

            <LabeledTextBox Label="@("Lokalizacja klienta".Tr())"
                            Value="@Order.CustomerLocationName"
                            DefaultValue=" "
                            TextBoxClass="w-full"
                            ReadOnly />

            <LabeledTextBox Label="@("Odział".Tr())"
                            Value="@Order.BranchName"
                            DefaultValue=" "
                            TextBoxClass="w-full"
                            ReadOnly />

            <LabeledTextBox Label="@("Nr. własny".Tr())"
                            Value="@Order.CustomerNo"
                            DefaultValue=" "
                            TextBoxClass="w-full"
                            ReadOnly />

            @if (Order.MessaNo > 0)
            {
                <LabeledTextBox Label="@("Nr. Messa".Tr())"
                                Value="@Order.MessaNo.ToString()"
                                DefaultValue=" "
                                TextBoxClass="w-full"
                                ReadOnly />
            }

            <LabeledTextBox Label="@("Zakład realizujący".Tr())"
                            Value="@Order.FactoryName"
                            DefaultValue=" "
                            TextBoxClass="w-full"
                            ReadOnly />
        </RadzenStack>

        @* Uwagi *@
        <RadzenRow Gap="1rem" class="">
            <RadzenColumn Size="12" SizeMD="6">
                <LabeledTextArea Label="@("Uwagi do zamówienia".Tr())"
                                 Value="@Order.Comments"
                                 DefaultValue=" "
                                 Rows="3"
                                 TextAreaClass="w-full"
                                 ReadOnly
                                 FormFieldClass="w-full" />
            </RadzenColumn>
        </RadzenRow>

        @* Lokalizacje *@
        <RadzenRow Gap="1rem" class="">
            <RadzenColumn Size="12" SizeMD="6">
                <LabeledField Label="@("Lokalizacja".Tr())" FormFieldClass="w-full">
                    <RadzenRow class="p-2">
                        <RadzenColumn>
                            @if (_mainLocalization != null)
                            {
                                <LocalizationTemplate Localization="_mainLocalization" ReadOnly />
                            }
                            else
                            {
                                <RadzenTextBox Value="@("Brak lokalizacji".Tr())" ReadOnly class="w-full" />
                            }
                        </RadzenColumn>
                    </RadzenRow>
                </LabeledField>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="6">
                <LabeledField Label="@("Adres dostawy".Tr())" FormFieldClass="w-full">
                    <RadzenRow class="p-2">
                        <RadzenColumn>
                            @if (Order.DifferentDeliveryLocalization && _deliveryLocalization != null)
                            {
                                <LocalizationTemplate Localization="_deliveryLocalization" ReadOnly />
                            }
                            else
                            {
                                <RadzenText class="m-0 my-1">
                                    @("Dostawa na adres główny".Tr())
                                </RadzenText>
                            }
                        </RadzenColumn>
                    </RadzenRow>
                </LabeledField>
            </RadzenColumn>
        </RadzenRow>

        <RadzenText class="m-0 text-sm">
            @("Utworzył".Tr()): @Order.CreatedByFullName @Order.CreatedDate.ToDisplayLocalDateTime()
        </RadzenText>
    </RadzenStack>

    <div class="-m-0.5">
        <OrderItemGrid OrderItems="Order.OrderItems"
                       ShowDraftColumn
                       ShowSaleColumn
                       ShowOptionValuesTable
                       ShowSheetsTable
                       AllowFiltering />
    </div>
</div>

@code {
    [Parameter] public required OrderResponse Order { get; set; }
    [Parameter] public string Class { get; set; } = "";

    private GetCustomerLocalizationsResponse? _mainLocalization;
    private GetCustomerLocalizationsResponse? _deliveryLocalization;

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        _mainLocalization = Order.Localizations.FirstOrDefault(x => x.Type == LocalizationType.Main);
        _deliveryLocalization = Order.Localizations.FirstOrDefault(x => x.Type == LocalizationType.Delivery);
    }

}
