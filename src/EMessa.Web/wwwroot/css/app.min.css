@import url('https://fonts.googleapis.com/css2?family=Jura:wght@300;400;700&display=swap');

/*@config "../../../../tailwind.config.js";*/

@tailwind base;

@tailwind components;

@tailwind utilities;

html, body, p, a {
  font-family: Jura, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
  font-size: 1em;
}

h1 {
  font-family: Jura, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

@media (max-width: 767.98px) {
  .main .top-row:not(.auth) {
    display: flex;
  }

  .main .top-row.auth {
    justify-content: space-between;
  }

  .main .top-row a, .main .top-row .btn-link {
    margin-left: 0;
  }
}

.sidebar {
  width: 270px;
  min-width: 270px;
  /*background-color: #7f1d1d;*/
  /*tailwind red 900*/
}

.sidebar .nav-item a {
  color: #f1f5f9;
  /* tailwind slate-100*/
  font: normal 12pt Jura;
  height: 2.5rem;
}

.sidebar .nav-item {
  padding-bottom: 0px;
  padding-top: 0px;
}

.e-btn:not(.e-flat, .e-round, .e-dropdown-btn), .e-bigger .e-btn:not(.e-flat, .e-round) {
  padding: 2px 6px;
  border-radius: 0;
  min-width: 90px;
}

.e-grid .e-unboundcelldiv > button {
  min-width: 0px;
}

.e-bigger .e-input-group.e-control-wrapper, .e-input-group.e-control-wrapper {
  border-radius: 0;
}

.bg-dark {
  background-color: #191917;
}

.bg-red {
  background-color: #e30010;
}

.color-red {
  color: #e30010;
}

.color-dark {
  color: #191917;
}

.e-btn{
  padding: 2px 6px;
  border-radius: 0;
}

.e-translate {
  content: url('data:image/svg+xml; utf8, <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m476-80 182-480h84L924-80h-84l-42-122H604L560-80h-84Zm152-192h144l-70-198h-4l-70 198Zm-468 72-56-56 202-202q-38-42-66.5-87T190-640h84q18 36 38.5 65t49.5 61q44-48 73-98.5T484-720H40v-80h280v-80h80v80h280v80H564q-21 71-57 138t-89 126l96 98-30 82-124-124-200 200Z"/></svg>');
}

.e-catalog {
  content: url('data:image/svg+xml; utf8, <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 -960 960 960" width="24"><path  stroke-width="2" d="M5 6L1 4.5v13.943L12 23l11-4.557V4l-4 2M5 16V2l7 3l7-3v14l-7 3zm6.95-11v14"/></svg>');
}

.e-plus-icon::before {
  content: '\e823';
}

.e-toast .e-btn {
  min-width: 0px;
}

.e-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn {
  min-width: 0px;
}

a.nav-link{
  padding: 8px 16px;
}

.main > div {
  padding-left: .5rem !important;
  padding-right: .5rem !important;
  padding-top: .1rem;
}

/* Wyrównanie Search do prawej w gridach SF */

.e-toolbar .e-toolbar-items {
  width: 100%;
}

/* Wyrównanie Search do prawej w gridach SF */

.e-toolbar .e-toolbar-item.e-spacer {
  flex-grow: 1;
}

/* Responsywne zachowanie dla ekranów mniejszych niż md (1007px) */

@media (max-width: 1007px) {
  .e-toolbar {
    width: 100%;
  }

  .e-toolbar .e-toolbar-item {
    align-self: flex-start;
  }

  .e-toolbar .e-toolbar-item.e-spacer {
    display: none;
    /* Opcjonalnie: ukrywa spacer na mobilnych */
  }
}

@font-face{
  font-family:Icons;

  src:url(../fonts/open-iconic.eot);

  src:url(../fonts/open-iconic.eot?#iconic-sm) format('embedded-opentype'),url(../fonts/open-iconic.woff) format('woff'),url(../fonts/open-iconic.ttf) format('truetype'),url(../fonts/open-iconic.otf) format('opentype'),url(../fonts/open-iconic.svg#iconic-sm) format('svg');

  font-weight:400;

  font-style:normal
}

.oi{
  position:relative;
  top:1px;
  display:inline-block;
  speak:none;
  font-family:Icons;
  font-style:normal;
  font-weight:400;
  line-height:1;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale
}

.oi:empty:before{
  width:1em;
  text-align:center;
  box-sizing:content-box
}

.oi.oi-align-center:before{
  text-align:center
}

.oi.oi-align-left:before{
  text-align:left
}

.oi.oi-align-right:before{
  text-align:right
}

.oi.oi-flip-horizontal:before{
  transform:scale(-1,1)
}

.oi.oi-flip-vertical:before{
  transform:scale(1,-1)
}

.oi.oi-flip-horizontal-vertical:before{
  transform:scale(-1,-1)
}

.oi-account-login:before{
  content:'\e000'
}

.oi-account-logout:before{
  content:'\e001'
}

.oi-action-redo:before{
  content:'\e002'
}

.oi-action-undo:before{
  content:'\e003'
}

.oi-align-center:before{
  content:'\e004'
}

.oi-align-left:before{
  content:'\e005'
}

.oi-align-right:before{
  content:'\e006'
}

.oi-aperture:before{
  content:'\e007'
}

.oi-arrow-bottom:before{
  content:'\e008'
}

.oi-arrow-circle-bottom:before{
  content:'\e009'
}

.oi-arrow-circle-left:before{
  content:'\e00a'
}

.oi-arrow-circle-right:before{
  content:'\e00b'
}

.oi-arrow-circle-top:before{
  content:'\e00c'
}

.oi-arrow-left:before{
  content:'\e00d'
}

.oi-arrow-right:before{
  content:'\e00e'
}

.oi-arrow-thick-bottom:before{
  content:'\e00f'
}

.oi-arrow-thick-left:before{
  content:'\e010'
}

.oi-arrow-thick-right:before{
  content:'\e011'
}

.oi-arrow-thick-top:before{
  content:'\e012'
}

.oi-arrow-top:before{
  content:'\e013'
}

.oi-audio-spectrum:before{
  content:'\e014'
}

.oi-audio:before{
  content:'\e015'
}

.oi-badge:before{
  content:'\e016'
}

.oi-ban:before{
  content:'\e017'
}

.oi-bar-chart:before{
  content:'\e018'
}

.oi-basket:before{
  content:'\e019'
}

.oi-battery-empty:before{
  content:'\e01a'
}

.oi-battery-full:before{
  content:'\e01b'
}

.oi-beaker:before{
  content:'\e01c'
}

.oi-bell:before{
  content:'\e01d'
}

.oi-bluetooth:before{
  content:'\e01e'
}

.oi-bold:before{
  content:'\e01f'
}

.oi-bolt:before{
  content:'\e020'
}

.oi-book:before{
  content:'\e021'
}

.oi-bookmark:before{
  content:'\e022'
}

.oi-box:before{
  content:'\e023'
}

.oi-briefcase:before{
  content:'\e024'
}

.oi-british-pound:before{
  content:'\e025'
}

.oi-browser:before{
  content:'\e026'
}

.oi-brush:before{
  content:'\e027'
}

.oi-bug:before{
  content:'\e028'
}

.oi-bullhorn:before{
  content:'\e029'
}

.oi-calculator:before{
  content:'\e02a'
}

.oi-calendar:before{
  content:'\e02b'
}

.oi-camera-slr:before{
  content:'\e02c'
}

.oi-caret-bottom:before{
  content:'\e02d'
}

.oi-caret-left:before{
  content:'\e02e'
}

.oi-caret-right:before{
  content:'\e02f'
}

.oi-caret-top:before{
  content:'\e030'
}

.oi-cart:before{
  content:'\e031'
}

.oi-chat:before{
  content:'\e032'
}

.oi-check:before{
  content:'\e033'
}

.oi-chevron-bottom:before{
  content:'\e034'
}

.oi-chevron-left:before{
  content:'\e035'
}

.oi-chevron-right:before{
  content:'\e036'
}

.oi-chevron-top:before{
  content:'\e037'
}

.oi-circle-check:before{
  content:'\e038'
}

.oi-circle-x:before{
  content:'\e039'
}

.oi-clipboard:before{
  content:'\e03a'
}

.oi-clock:before{
  content:'\e03b'
}

.oi-cloud-download:before{
  content:'\e03c'
}

.oi-cloud-upload:before{
  content:'\e03d'
}

.oi-cloud:before{
  content:'\e03e'
}

.oi-cloudy:before{
  content:'\e03f'
}

.oi-code:before{
  content:'\e040'
}

.oi-cog:before{
  content:'\e041'
}

.oi-collapse-down:before{
  content:'\e042'
}

.oi-collapse-left:before{
  content:'\e043'
}

.oi-collapse-right:before{
  content:'\e044'
}

.oi-collapse-up:before{
  content:'\e045'
}

.oi-command:before{
  content:'\e046'
}

.oi-comment-square:before{
  content:'\e047'
}

.oi-compass:before{
  content:'\e048'
}

.oi-contrast:before{
  content:'\e049'
}

.oi-copywriting:before{
  content:'\e04a'
}

.oi-credit-card:before{
  content:'\e04b'
}

.oi-crop:before{
  content:'\e04c'
}

.oi-dashboard:before{
  content:'\e04d'
}

.oi-data-transfer-download:before{
  content:'\e04e'
}

.oi-data-transfer-upload:before{
  content:'\e04f'
}

.oi-delete:before{
  content:'\e050'
}

.oi-dial:before{
  content:'\e051'
}

.oi-document:before{
  content:'\e052'
}

.oi-dollar:before{
  content:'\e053'
}

.oi-double-quote-sans-left:before{
  content:'\e054'
}

.oi-double-quote-sans-right:before{
  content:'\e055'
}

.oi-double-quote-serif-left:before{
  content:'\e056'
}

.oi-double-quote-serif-right:before{
  content:'\e057'
}

.oi-droplet:before{
  content:'\e058'
}

.oi-eject:before{
  content:'\e059'
}

.oi-elevator:before{
  content:'\e05a'
}

.oi-ellipses:before{
  content:'\e05b'
}

.oi-envelope-closed:before{
  content:'\e05c'
}

.oi-envelope-open:before{
  content:'\e05d'
}

.oi-euro:before{
  content:'\e05e'
}

.oi-excerpt:before{
  content:'\e05f'
}

.oi-expand-down:before{
  content:'\e060'
}

.oi-expand-left:before{
  content:'\e061'
}

.oi-expand-right:before{
  content:'\e062'
}

.oi-expand-up:before{
  content:'\e063'
}

.oi-external-link:before{
  content:'\e064'
}

.oi-eye:before{
  content:'\e065'
}

.oi-eyedropper:before{
  content:'\e066'
}

.oi-file:before{
  content:'\e067'
}

.oi-fire:before{
  content:'\e068'
}

.oi-flag:before{
  content:'\e069'
}

.oi-flash:before{
  content:'\e06a'
}

.oi-folder:before{
  content:'\e06b'
}

.oi-fork:before{
  content:'\e06c'
}

.oi-fullscreen-enter:before{
  content:'\e06d'
}

.oi-fullscreen-exit:before{
  content:'\e06e'
}

.oi-globe:before{
  content:'\e06f'
}

.oi-graph:before{
  content:'\e070'
}

.oi-grid-four-up:before{
  content:'\e071'
}

.oi-grid-three-up:before{
  content:'\e072'
}

.oi-grid-two-up:before{
  content:'\e073'
}

.oi-hard-drive:before{
  content:'\e074'
}

.oi-header:before{
  content:'\e075'
}

.oi-headphones:before{
  content:'\e076'
}

.oi-heart:before{
  content:'\e077'
}

.oi-home:before{
  content:'\e078'
}

.oi-image:before{
  content:'\e079'
}

.oi-inbox:before{
  content:'\e07a'
}

.oi-infinity:before{
  content:'\e07b'
}

.oi-info:before{
  content:'\e07c'
}

.oi-italic:before{
  content:'\e07d'
}

.oi-justify-center:before{
  content:'\e07e'
}

.oi-justify-left:before{
  content:'\e07f'
}

.oi-justify-right:before{
  content:'\e080'
}

.oi-key:before{
  content:'\e081'
}

.oi-laptop:before{
  content:'\e082'
}

.oi-layers:before{
  content:'\e083'
}

.oi-lightbulb:before{
  content:'\e084'
}

.oi-link-broken:before{
  content:'\e085'
}

.oi-link-intact:before{
  content:'\e086'
}

.oi-list-rich:before{
  content:'\e087'
}

.oi-list:before{
  content:'\e088'
}

.oi-location:before{
  content:'\e089'
}

.oi-lock-locked:before{
  content:'\e08a'
}

.oi-lock-unlocked:before{
  content:'\e08b'
}

.oi-loop-circular:before{
  content:'\e08c'
}

.oi-loop-square:before{
  content:'\e08d'
}

.oi-loop:before{
  content:'\e08e'
}

.oi-magnifying-glass:before{
  content:'\e08f'
}

.oi-map-marker:before{
  content:'\e090'
}

.oi-map:before{
  content:'\e091'
}

.oi-media-pause:before{
  content:'\e092'
}

.oi-media-play:before{
  content:'\e093'
}

.oi-media-record:before{
  content:'\e094'
}

.oi-media-skip-backward:before{
  content:'\e095'
}

.oi-media-skip-forward:before{
  content:'\e096'
}

.oi-media-step-backward:before{
  content:'\e097'
}

.oi-media-step-forward:before{
  content:'\e098'
}

.oi-media-stop:before{
  content:'\e099'
}

.oi-medical-cross:before{
  content:'\e09a'
}

.oi-menu:before{
  content:'\e09b'
}

.oi-microphone:before{
  content:'\e09c'
}

.oi-minus:before{
  content:'\e09d'
}

.oi-monitor:before{
  content:'\e09e'
}

.oi-moon:before{
  content:'\e09f'
}

.oi-move:before{
  content:'\e0a0'
}

.oi-musical-note:before{
  content:'\e0a1'
}

.oi-paperclip:before{
  content:'\e0a2'
}

.oi-pencil:before{
  content:'\e0a3'
}

.oi-people:before{
  content:'\e0a4'
}

.oi-person:before{
  content:'\e0a5'
}

.oi-phone:before{
  content:'\e0a6'
}

.oi-pie-chart:before{
  content:'\e0a7'
}

.oi-pin:before{
  content:'\e0a8'
}

.oi-play-circle:before{
  content:'\e0a9'
}

.oi-plus:before{
  content:'\e0aa'
}

.oi-power-standby:before{
  content:'\e0ab'
}

.oi-print:before{
  content:'\e0ac'
}

.oi-project:before{
  content:'\e0ad'
}

.oi-pulse:before{
  content:'\e0ae'
}

.oi-puzzle-piece:before{
  content:'\e0af'
}

.oi-question-mark:before{
  content:'\e0b0'
}

.oi-rain:before{
  content:'\e0b1'
}

.oi-random:before{
  content:'\e0b2'
}

.oi-reload:before{
  content:'\e0b3'
}

.oi-resize-both:before{
  content:'\e0b4'
}

.oi-resize-height:before{
  content:'\e0b5'
}

.oi-resize-width:before{
  content:'\e0b6'
}

.oi-rss-alt:before{
  content:'\e0b7'
}

.oi-rss:before{
  content:'\e0b8'
}

.oi-script:before{
  content:'\e0b9'
}

.oi-share-boxed:before{
  content:'\e0ba'
}

.oi-share:before{
  content:'\e0bb'
}

.oi-shield:before{
  content:'\e0bc'
}

.oi-signal:before{
  content:'\e0bd'
}

.oi-signpost:before{
  content:'\e0be'
}

.oi-sort-ascending:before{
  content:'\e0bf'
}

.oi-sort-descending:before{
  content:'\e0c0'
}

.oi-spreadsheet:before{
  content:'\e0c1'
}

.oi-star:before{
  content:'\e0c2'
}

.oi-sun:before{
  content:'\e0c3'
}

.oi-tablet:before{
  content:'\e0c4'
}

.oi-tag:before{
  content:'\e0c5'
}

.oi-tags:before{
  content:'\e0c6'
}

.oi-target:before{
  content:'\e0c7'
}

.oi-task:before{
  content:'\e0c8'
}

.oi-terminal:before{
  content:'\e0c9'
}

.oi-text:before{
  content:'\e0ca'
}

.oi-thumb-down:before{
  content:'\e0cb'
}

.oi-thumb-up:before{
  content:'\e0cc'
}

.oi-timer:before{
  content:'\e0cd'
}

.oi-transfer:before{
  content:'\e0ce'
}

.oi-trash:before{
  content:'\e0cf'
}

.oi-underline:before{
  content:'\e0d0'
}

.oi-vertical-align-bottom:before{
  content:'\e0d1'
}

.oi-vertical-align-center:before{
  content:'\e0d2'
}

.oi-vertical-align-top:before{
  content:'\e0d3'
}

.oi-video:before{
  content:'\e0d4'
}

.oi-volume-high:before{
  content:'\e0d5'
}

.oi-volume-low:before{
  content:'\e0d6'
}

.oi-volume-off:before{
  content:'\e0d7'
}

.oi-warning:before{
  content:'\e0d8'
}

.oi-wifi:before{
  content:'\e0d9'
}

.oi-wrench:before{
  content:'\e0da'
}

.oi-x:before{
  content:'\e0db'
}

.oi-yen:before{
  content:'\e0dc'
}

.oi-zoom-in:before{
  content:'\e0dd'
}

.oi-zoom-out:before{
  content:'\e0de'
}

html, body {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

h1:focus {
  outline: none;
}

a, .btn-link {
  color: #0071c1;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

app {
  position: relative;
  display: flex;
  flex-direction: column;
}

.top-row {
  height: 3.5rem;
  display: flex;
  align-items: center;
}

.main {
  flex: 1;
}

.main .top-row {
  background-color: #f7f7f7;
  border-bottom: 1px solid #d6d5d5;
  justify-content: flex-end;
}

.main .top-row > a, .main .top-row .btn-link {
  white-space: nowrap;
  margin-left: 1.5rem;
}

.main .top-row a:first-child {
  overflow: hidden;
  text-overflow: ellipsis;
}

.e-filemanager .e-fe-popup.e-dialog.e-popup.e-dlg-resizable {
  z-index: 1202 !important;
}

.sidebar {
  background-color: #31373b;
}

.sidebar .top-row {
  background-color: rgba(0,0,0,0.4);
}

.sidebar .navbar-brand {
  font-size: 1.1rem;
}

.sidebar .oi {
  width: 2rem;
  font-size: 1.1rem;
  vertical-align: text-top;
  top: -2px;
}

.sidebar .nav-item {
  font-size: 0.9rem;
  padding-bottom: 0.5rem;
}

.sidebar .nav-item:first-of-type {
  padding-top: 1rem;
}

.sidebar .nav-item:last-of-type {
  padding-bottom: 1rem;
}

.sidebar .nav-item a {
  color: #d7d7d7;
  border-radius: 4px;
  height: 3rem;
  display: flex;
  align-items: center;
  line-height: 3rem;
}

.sidebar .nav-item a.active {
  background-color: #D1D5DB;
  /* gray-300 */
  color: #374151;
  /* gray-700 - ciemny tekst dla lepszej czytelności */
}

.sidebar .nav-item a:hover {
  background-color: #F3F4F6;
  /* gray-100 */
  color: #374151;
  /* gray-700 - ten sam kolor tekstu co w active */
}

.sidebar .nav-menu {
  background-color: #E5E7EB;
  /* gray-200 */
  display: flex;
  flex-direction: column;
  width: 270px;
  height: calc(100dvh - 48px);
}

.sidebar .displayText {
  color: black;
}

/* Menu item */

.e-sidebar span.e-icons {
  color: black;
}

/* Scrollbar */

.sidebar .scroller {
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar .scroller::-webkit-scrollbar {
  width: 8px;
}

.sidebar .scroller::-webkit-scrollbar-thumb {
  background-color: rgb(156 163 175);
  /* gray-400 */
  border-radius: 4px;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.valid.modified:not([type=checkbox]) {
  outline: 1px solid #26b050;
}

.invalid {
  outline: 1px solid red;
}

.validation-message {
  color: red;
}

#blazor-error-ui {
  background: lightyellow;
  bottom: 0;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  padding: 0.6rem 1.25rem 0.7rem 1.25rem;
  position: fixed;
  width: 100%;
  z-index: 1000;
}

#blazor-error-ui .dismiss {
  cursor: pointer;
  position: absolute;
  right: 0.75rem;
  top: 0.5rem;
}

.blazor-error-boundary {
  background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
  padding: 1rem 1rem 1rem 3.7rem;
  color: white;
}

.blazor-error-boundary::after {
  content: "An error has occurred."
}

@media (max-width: 767.98px) {
  .main .top-row:not(.auth) {
    display: none;
  }

  .main .top-row.auth {
    justify-content: space-between;
  }

  .main .top-row a, .main .top-row .btn-link {
    margin-left: 0;
  }
}

@media (min-width: 768px) {
  app {
    flex-direction: row;
  }

  .sidebar {
    width: 250px;
    min-width: 250px;
    height: auto;
    min-height: 100vh;
    position: sticky;
    top: 0;
  }

  .main .top-row {
    position: sticky;
    top: 0;
  }

  .main > div {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    padding-top: 1.1rem;
  }

  .navbar-toggler {
    display: none;
  }

  .sidebar .collapse {
    /* Never collapse the sidebar for wide screens */
    display: block;
  }
}

/* Customized messages for disconnected server */

/* https://codyanhorn.tech/blog/blazor/2020/06/03/Blazor-Custom-Connection-Details-Display.html */

.custom-reconnect-modal > div {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
  text-align: center;
  font-weight: bold;
}

.components-reconnect-hide > div {
  display: none;
}

.components-reconnect-show > div {
  display: none;
}

.components-reconnect-show > .show {
  display: block;
}

.components-reconnect-failed > div {
  display: none;
}

.components-reconnect-failed > .failed {
  display: block;
}

.components-reconnect-rejected > div {
  display: none;
}

.components-reconnect-rejected > .rejected {
  display: block;
}

.components-reconnect-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.75);
  z-index: 50;
}

.components-reconnect-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 28rem;
  width: 100%;
  margin-left: 1rem;
  margin-right: 1rem;
  text-align: center;
  border: 1px solid red;
}

@media screen and (max-width: 600px) {
  .components-reconnect-box {
    max-width: 16rem;
    width: 100%;
    margin-left: 0.25rem;
    margin-right: 0.25rem;
  }
}

.components-reconnect-paragraf {
  color: rgb(55, 65, 81);
  font-size: 1rem;
}

.components-reconnect-button {
  background-color: #808080;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 200ms;
  border: 1px solid red;
}

.components-reconnect-button:hover {
  background-color: #696969;
}

/* End of customized messages for disconnected server */

.site-hidden {
  visibility: hidden;
  height: 0;
}

.site-visible {
  visibility: visible;
  height: initial;
}

.fas {
  font-family: 'Font Awesome 6 Free' !important;
  font-weight: 900;
  font-style: normal;
}

:root {
}

body {
  position: relative;
  background: #FFFFFF;
}

/*dodaj w kontenerze aby inne style zadziałały*/

.emessa {
}

/* Domyślny wygląd ikonek Radzen (Google Material Icons) */

.rzi {
  font-variation-settings: 'FILL' 0, 'wght' 300, 'GRAD' 0, 'opsz' 48;
  font-size: 28px;
}

/* Domyślny wygląd ikonek Radzen (Font Awesome) */

@font-face {
  font-family: 'Font Awesome 6 Free';

  font-style: normal;

  font-weight: 900;

  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
}

.rzi.font-awesome {
  font-family: 'Font Awesome 6 Free' !important;
  font-weight: 900;
  font-style: normal;
  font-size: 20px;
}

/* RZ */

:root {
  --esa-size-16: 16px;
  --esa-size-24: 24px;
  --esa-size-32: 32px;
}

.esa-button-size {
  width: var(--esa-size-16);
  height: var(--esa-size-16);
}

/* RZ */

.bg-add{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1)) !important;
}

.bg-edit{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-todo{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-add{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-edit{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-delete{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-add-form{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-edit-form{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1)) !important;
}

.bg-esa-disabled {
  background-color: var(--rz-base-200);
}

.esa-actions-row{
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 0.5rem;
}

.esa-actions-col{
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

/* RX TABS */

.rz-tabview-nav {
  flex-wrap: wrap !important;
}

/* RX TABS */

/* RX GRID */

.rz-grid-table-striped tbody tr.bg-row-selected td{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1)) !important;
}

/* RX GRID */

/* RZ CHECKBOX */

:root {
  --chkbox-border-width: 2px;
  --chkbox-icon-color: var(--rz-base-light);
  --chkbox-size: 18px;
  --checkbox-icon-size: var(--chkbox-size);
  --rz-checkbox-icon-width: var(--chkbox-size);
  --rz-checkbox-icon-height: var(--chkbox-size);
  --rz-checkbox-icon-font-size: var(--chkbox-size);
  --rz-checkbox-width: var(--chkbox-size);
  --rz-checkbox-height: var(--chkbox-size);
  /*Enabled colors*/
  --chkbox-enabled-active-color: var(--rz-primary);
  /*Active Background*/
  --chkbox-enabled-inactive-color: var(--rz-base-light);
  /*Inactive Background*/
  --chkbox-enabled-border-color: var(--rz-primary);
  /*Active Border*/
  /*Disabled colors*/
  --chkbox-disabled-active-color: var(--rz-base-dark);
  /*Inactive Background*/
  --chkbox-disabled-inactive-color: var(--rz-base-light);
  /*Inactive Background*/
  --chkbox-disabled-border-color: var(--rz-base-dark);
  /*Inactive Border*/
}

.rz-chkbox-box .rzi-check:before {
  content: "check";
  font-variation-settings: 'FILL' 1, 'wght' 600, 'GRAD' 0, 'opsz' 48;
  font-weight: bold;
}

.rz-chkbox-box .rzi-times:before {
  content: "remove";
  font-variation-settings: 'FILL' 1, 'wght' 600, 'GRAD' 0, 'opsz' 48;
  font-weight: bold;
}

.rz-chkbox-box {
  position: absolute;
  cursor: pointer;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: var(--rz-input-border);
  border-width: var(--chkbox-border-width);
  border-radius: var(--rz-checkbox-border-radius);
  border-color: var(--chkbox-enabled-border-color);
  box-shadow: var(--rz-input-shadow);
  background-color: var(--chkbox-enabled-inactive-color);
  transition: var(--rz-transition-all);
}

.rz-chkbox-box:hover:not(.rz-state-disabled) {
  border-width: var(--chkbox-border-width);
}

.rz-chkbox-box.rz-state-disabled {
  cursor: initial;
  color: var(--rz-input-disabled-color);
  box-shadow: var(--rz-input-disabled-shadow);
  background-color: var(--chkbox-disabled-inactive-color);
  border-color: var(--chkbox-disabled-border-color);
  border-width: var(--chkbox-border-width);
  opacity: var(--rz-input-disabled-opacity);
}

.rz-chkbox-box .rzi {
  width: var(--checkbox-icon-size);
  height: var(--checkbox-icon-size);
  font-size: var(--rz-checkbox-icon-font-size);
  color: var(--chkbox-icon-color);
  vertical-align: middle;
  background-color: transparent;
  border-radius: var(--rz-checkbox-checked-icon-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dla aktywnego (zaznaczonego) stanu */

.rz-chkbox-box.rz-state-active {
  background-color: var(--chkbox-enabled-active-color);
  border-color: var(--chkbox-enabled-active-color);
}

/* Dla aktywnego stanu po najechaniu */

.rz-chkbox-box.rz-state-active:hover:not(.rz-state-disabled) {
  background-color: var(--chkbox-enabled-active-color);
  border-color: var(--chkbox-enabled-active-color);
}

/* Dla wyłączonego aktywnego stanu */

.rz-chkbox-box.rz-state-active.rz-state-disabled {
  background-color: var(--chkbox-disabled-active-color);
  border-color: var(--chkbox-disabled-active-color);
  opacity: 0.5;
}

/* RZ CHECKBOX */

/* RZ NUMERIC */

:root {
  --emessa-numeric-color: var(--rz-text-color);
}

.emessa .rz-numeric,
.emessa .rz-numeric .rz-inputtext {
  border-radius: var(--rz-input-border-radius);
  border-color: var(--emessa-numeric-color);
}

.emessa .rz-numeric.rz-state-disabled,
.emessa .rz-numeric:disabled {
  border-color: var(--emessa-numeric-color);
  opacity: 0.7;
}

.emessa .rz-numeric.rz-state-disabled .rz-inputtext,
.emessa .rz-numeric:disabled .rz-inputtext {
  color: var(--emessa-numeric-color);
  -webkit-text-fill-color: var(--emessa-numeric-color);
}

/* RZ NUMERIC */

/* RZ DROPDOWN */

/* Custom styling for disabled RadzenDropDown to improve visibility */

.emessa .rz-dropdown.rz-state-disabled,
.emessa .rz-dropdown:disabled {
  opacity: 0.7;
  background-color: var(--rz-base-200);
}

.emessa .rz-dropdown.rz-state-disabled .rz-inputtext,
.emessa .rz-dropdown:disabled .rz-inputtext {
  color: var(--rz-base-900);
  -webkit-text-fill-color: var(--rz-base-900);
  background-color: var(--rz-base-200);
}

.emessa .rz-dropdown.rz-state-disabled .rz-dropdown-label,
.emessa .rz-dropdown:disabled .rz-dropdown-label {
  color: var(--rz-base-900);
  -webkit-text-fill-color: var(--rz-base-900);
}

/* Disabled dropdown border styling - ciemniejszy border tylko dla disabled */

.emessa .rz-dropdown.rz-state-disabled,
.emessa .rz-dropdown:disabled {
  border-color: var(--rz-base-400) !important;
}

/* Warning dropdown class for validation styling */

.emessa .warning-dropdown {
  border-color: var(--rz-warning) !important;
  box-shadow: 0 0 0 1px var(--rz-warning) !important;
}

.emessa .warning-dropdown:focus {
  border-color: var(--rz-base-400) !important;
  box-shadow: 0 0 0 2px var(--rz-warning-lighter) !important;
}

/* RZ DROPDOWN */

@media (max-width: 768px) {
  .rz-dialog:not(.rz-dialog-confirm):not(.rz-dialog-alert) {
    position: absolute;
    width: 100% !important;
    max-width: 95vw !important;
    max-height: 90vh !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    border-radius: 0 !important;
  }
}

/* Zakładki (Tabs) zawijają się, gdy nie mieszczą się na ekranie. */

ul[role=tablist] {
  flex-wrap: wrap;
}

/* Zmniejszenie domyślnego paddingu 20px dla contentu RadzenFieldset */

.rz-fieldset-content {
  padding: 14px;
}

/* Radzen dialog content */

.rz-dialog {
  padding: 0.5rem;
}

.rz-dialog-content {
  padding: 0.5rem;
}

.highlighted-grid-row td {
  background-color: var(--color-emerald-300) !important;
  color: #000;
}

.highlighted-grid-row:hover td {
  background-color: var(--color-emerald-400) !important;
}

/* Nadpisanie domyślnego stylu nagłówka RadzenDataGrid */

.rz-custom-header {
  width: 100%;
}

.f-row{
  display: flex;
  flex-direction: row;
}

.f-col{
  display: flex;
  flex-direction: column;
}

.f-row-1{
  display: flex;
  flex-direction: row;
  gap: 0.25rem;
}

.f-col-1{
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.f-row-2{
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
}

.f-col-2{
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.f-row-3{
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
}

.f-col-3{
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.f-row-4{
  display: flex;
  flex-direction: row;
  gap: 1rem;
}

.f-col-4{
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.f-row-5{
  display: flex;
  flex-direction: row;
  gap: 1.25rem;
}

.f-col-5{
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/*@import './syncfusion-blazor-icons.css';*/

/*@import './syncfusion-custom.css';*/

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root{
  --color-emerald-300: #6ee7b7;
  --color-emerald-400: #34d399;
}

.container{
  width: 100%;
}

@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}

@media (min-width: 1008px){
  .container{
    max-width: 1008px;
  }
}

@media (min-width: 1600px){
  .container{
    max-width: 1600px;
  }
}

@media (min-width: 1920px){
  .container{
    max-width: 1920px;
  }
}

@media (min-width: 2560px){
  .container{
    max-width: 2560px;
  }
}

.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.not-sr-only{
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.pointer-events-none{
  pointer-events: none;
}

.pointer-events-auto{
  pointer-events: auto;
}

.\!visible{
  visibility: visible !important;
}

.visible{
  visibility: visible;
}

.invisible{
  visibility: hidden;
}

.collapse{
  visibility: collapse;
}

.static{
  position: static;
}

.\!fixed{
  position: fixed !important;
}

.fixed{
  position: fixed;
}

.\!absolute{
  position: absolute !important;
}

.absolute{
  position: absolute;
}

.relative{
  position: relative;
}

.sticky{
  position: sticky;
}

.inset-0{
  inset: 0px;
}

.-bottom-\[47px\]{
  bottom: -47px;
}

.-left-\[15px\]{
  left: -15px;
}

.-left-\[9999px\]{
  left: -9999px;
}

.-top-\[18px\]{
  top: -18px;
}

.-top-\[21px\]{
  top: -21px;
}

.-top-\[35px\]{
  top: -35px;
}

.bottom-0{
  bottom: 0px;
}

.bottom-0\.5{
  bottom: 0.125rem;
}

.bottom-1\/2{
  bottom: 50%;
}

.left-0{
  left: 0px;
}

.left-1\/2{
  left: 50%;
}

.left-3{
  left: 0.75rem;
}

.left-\[50\%\]{
  left: 50%;
}

.left-\[50px\]{
  left: 50px;
}

.left-\[calc\(50\%-1px\)\]{
  left: calc(50% - 1px);
}

.right-0{
  right: 0px;
}

.right-0\.5{
  right: 0.125rem;
}

.right-1{
  right: 0.25rem;
}

.right-1\.5{
  right: 0.375rem;
}

.right-2{
  right: 0.5rem;
}

.right-3{
  right: 0.75rem;
}

.right-9{
  right: 2.25rem;
}

.right-\[20px\]{
  right: 20px;
}

.top-0{
  top: 0px;
}

.top-1{
  top: 0.25rem;
}

.top-1\/2{
  top: 50%;
}

.top-2{
  top: 0.5rem;
}

.top-\[11px\]{
  top: 11px;
}

.top-\[13px\]{
  top: 13px;
}

.top-\[50\%\]{
  top: 50%;
}

.top-\[50px\]{
  top: 50px;
}

.top-\[60px\]{
  top: 60px;
}

.top-full{
  top: 100%;
}

.isolate{
  isolation: isolate;
}

.isolation-auto{
  isolation: auto;
}

.\!z-40{
  z-index: 40 !important;
}

.z-0{
  z-index: 0;
}

.z-10{
  z-index: 10;
}

.z-20{
  z-index: 20;
}

.z-30{
  z-index: 30;
}

.z-40{
  z-index: 40;
}

.z-50{
  z-index: 50;
}

.z-\[1000\]{
  z-index: 1000;
}

.z-\[1001\]{
  z-index: 1001;
}

.z-\[100\]{
  z-index: 100;
}

.z-\[1035\]{
  z-index: 1035;
}

.z-\[1040\]{
  z-index: 1040;
}

.z-\[1065\]{
  z-index: 1065;
}

.z-\[1066\]{
  z-index: 1066;
}

.z-\[1070\]{
  z-index: 1070;
}

.z-\[1080\]{
  z-index: 1080;
}

.z-\[1100\]{
  z-index: 1100;
}

.z-\[2\]{
  z-index: 2;
}

.z-\[999\]{
  z-index: 999;
}

.order-1{
  order: 1;
}

.order-2{
  order: 2;
}

.order-3{
  order: 3;
}

.float-start{
  float: inline-start;
}

.float-end{
  float: inline-end;
}

.float-right{
  float: right;
}

.float-left{
  float: left;
}

.float-none{
  float: none;
}

.clear-start{
  clear: inline-start;
}

.clear-end{
  clear: inline-end;
}

.clear-left{
  clear: left;
}

.clear-right{
  clear: right;
}

.clear-both{
  clear: both;
}

.clear-none{
  clear: none;
}

.\!-m-px{
  margin: -1px !important;
}

.-m-2{
  margin: -0.5rem;
}

.-m-px{
  margin: -1px;
}

.m-0{
  margin: 0px;
}

.m-0\.5{
  margin: 0.125rem;
}

.m-1{
  margin: 0.25rem;
}

.m-2{
  margin: 0.5rem;
}

.m-3{
  margin: 0.75rem;
}

.m-auto{
  margin: auto;
}

.\!my-0{
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}

.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-\[10px\]{
  margin-left: 10px;
  margin-right: 10px;
}

.mx-auto{
  margin-left: auto;
  margin-right: auto;
}

.my-0{
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-\[5px\]{
  margin-top: 5px;
  margin-bottom: 5px;
}

.-ml-\[1\.5rem\]{
  margin-left: -1.5rem;
}

.-mt-1{
  margin-top: -0.25rem;
}

.-mt-3{
  margin-top: -0.75rem;
}

.mb-0{
  margin-bottom: 0px;
}

.mb-1{
  margin-bottom: 0.25rem;
}

.mb-2{
  margin-bottom: 0.5rem;
}

.mb-2\.5{
  margin-bottom: 0.625rem;
}

.mb-3{
  margin-bottom: 0.75rem;
}

.mb-5{
  margin-bottom: 1.25rem;
}

.mb-8{
  margin-bottom: 2rem;
}

.mb-\[0\.125rem\]{
  margin-bottom: 0.125rem;
}

.mb-\[10px\]{
  margin-bottom: 10px;
}

.me-2{
  margin-inline-end: 0.5rem;
}

.ml-1{
  margin-left: 0.25rem;
}

.ml-2{
  margin-left: 0.5rem;
}

.ml-3{
  margin-left: 0.75rem;
}

.ml-4{
  margin-left: 1rem;
}

.ml-6{
  margin-left: 1.5rem;
}

.ml-\[30px\]{
  margin-left: 30px;
}

.ml-\[3px\]{
  margin-left: 3px;
}

.ml-auto{
  margin-left: auto;
}

.mr-0{
  margin-right: 0px;
}

.mr-1{
  margin-right: 0.25rem;
}

.mr-2{
  margin-right: 0.5rem;
}

.mr-3{
  margin-right: 0.75rem;
}

.mr-4{
  margin-right: 1rem;
}

.mr-5{
  margin-right: 1.25rem;
}

.mr-6{
  margin-right: 1.5rem;
}

.mr-\[6px\]{
  margin-right: 6px;
}

.mr-\[8px\]{
  margin-right: 8px;
}

.mr-auto{
  margin-right: auto;
}

.mt-0{
  margin-top: 0px;
}

.mt-0\.5{
  margin-top: 0.125rem;
}

.mt-1{
  margin-top: 0.25rem;
}

.mt-2{
  margin-top: 0.5rem;
}

.mt-2\.5{
  margin-top: 0.625rem;
}

.mt-3{
  margin-top: 0.75rem;
}

.mt-4{
  margin-top: 1rem;
}

.mt-\[0\.15rem\]{
  margin-top: 0.15rem;
}

.box-border{
  box-sizing: border-box;
}

.box-content{
  box-sizing: content-box;
}

.line-clamp-none{
  overflow: visible;
  display: block;
  -webkit-box-orient: horizontal;
  -webkit-line-clamp: none;
}

.\!block{
  display: block !important;
}

.block{
  display: block;
}

.inline-block{
  display: inline-block;
}

.inline{
  display: inline;
}

.flex{
  display: flex;
}

.inline-flex{
  display: inline-flex;
}

.table{
  display: table;
}

.inline-table{
  display: inline-table;
}

.table-caption{
  display: table-caption;
}

.table-cell{
  display: table-cell;
}

.table-column{
  display: table-column;
}

.table-column-group{
  display: table-column-group;
}

.table-footer-group{
  display: table-footer-group;
}

.table-header-group{
  display: table-header-group;
}

.table-row-group{
  display: table-row-group;
}

.table-row{
  display: table-row;
}

.flow-root{
  display: flow-root;
}

.grid{
  display: grid;
}

.inline-grid{
  display: inline-grid;
}

.contents{
  display: contents;
}

.list-item{
  display: list-item;
}

.hidden{
  display: none;
}

.size-16{
  width: 4rem;
  height: 4rem;
}

.\!h-0{
  height: 0px !important;
}

.\!h-px{
  height: 1px !important;
}

.h-0{
  height: 0px;
}

.h-1{
  height: 0.25rem;
}

.h-1\.5{
  height: 0.375rem;
}

.h-10{
  height: 2.5rem;
}

.h-14{
  height: 3.5rem;
}

.h-2\/5{
  height: 40%;
}

.h-24{
  height: 6rem;
}

.h-3{
  height: 0.75rem;
}

.h-32{
  height: 8rem;
}

.h-4{
  height: 1rem;
}

.h-5{
  height: 1.25rem;
}

.h-6{
  height: 1.5rem;
}

.h-7{
  height: 1.75rem;
}

.h-8{
  height: 2rem;
}

.h-9{
  height: 2.25rem;
}

.h-\[0\.9375rem\]{
  height: 0.9375rem;
}

.h-\[1\.125rem\]{
  height: 1.125rem;
}

.h-\[1\.4rem\]{
  height: 1.4rem;
}

.h-\[100px\]{
  height: 100px;
}

.h-\[10px\]{
  height: 10px;
}

.h-\[120px\]{
  height: 120px;
}

.h-\[160px\]{
  height: 160px;
}

.h-\[260px\]{
  height: 260px;
}

.h-\[2px\]{
  height: 2px;
}

.h-\[30px\]{
  height: 30px;
}

.h-\[32px\]{
  height: 32px;
}

.h-\[380px\]{
  height: 380px;
}

.h-\[400px\]{
  height: 400px;
}

.h-\[40px\]{
  height: 40px;
}

.h-\[42px\]{
  height: 42px;
}

.h-\[48px\]{
  height: 48px;
}

.h-\[4px\]{
  height: 4px;
}

.h-\[50px\]{
  height: 50px;
}

.h-\[512px\]{
  height: 512px;
}

.h-\[56px\]{
  height: 56px;
}

.h-\[600px\]{
  height: 600px;
}

.h-\[6px\]{
  height: 6px;
}

.h-\[72px\]{
  height: 72px;
}

.h-\[calc\(100\%-100px\)\]{
  height: calc(100% - 100px);
}

.h-auto{
  height: auto;
}

.h-full{
  height: 100%;
}

.h-px{
  height: 1px;
}

.h-screen{
  height: 100vh;
}

.max-h-\[300px\]{
  max-height: 300px;
}

.max-h-\[400px\]{
  max-height: 400px;
}

.max-h-\[70vh\]{
  max-height: 70vh;
}

.max-h-\[calc\(100\%-64px\)\]{
  max-height: calc(100% - 64px);
}

.max-h-\[calc\(100dvh-100px\)\]{
  max-height: calc(100dvh - 100px);
}

.max-h-full{
  max-height: 100%;
}

.min-h-8{
  min-height: 2rem;
}

.min-h-\[1\.5rem\]{
  min-height: 1.5rem;
}

.min-h-\[100px\]{
  min-height: 100px;
}

.min-h-\[300px\]{
  min-height: 300px;
}

.min-h-\[305px\]{
  min-height: 305px;
}

.min-h-\[325px\]{
  min-height: 325px;
}

.min-h-\[3rem\]{
  min-height: 3rem;
}

.min-h-\[40px\]{
  min-height: 40px;
}

.min-h-\[50px\]{
  min-height: 50px;
}

.min-h-\[90px\]{
  min-height: 90px;
}

.min-h-\[auto\]{
  min-height: auto;
}

.min-h-full{
  min-height: 100%;
}

.min-h-screen{
  min-height: 100vh;
}

.\!w-px{
  width: 1px !important;
}

.w-0{
  width: 0px;
}

.w-1\.5{
  width: 0.375rem;
}

.w-1\/2{
  width: 50%;
}

.w-10{
  width: 2.5rem;
}

.w-12{
  width: 3rem;
}

.w-14{
  width: 3.5rem;
}

.w-16{
  width: 4rem;
}

.w-2{
  width: 0.5rem;
}

.w-2\/12{
  width: 16.666667%;
}

.w-20{
  width: 5rem;
}

.w-28{
  width: 7rem;
}

.w-3{
  width: 0.75rem;
}

.w-32{
  width: 8rem;
}

.w-4{
  width: 1rem;
}

.w-4\/12{
  width: 33.333333%;
}

.w-5{
  width: 1.25rem;
}

.w-52{
  width: 13rem;
}

.w-56{
  width: 14rem;
}

.w-6{
  width: 1.5rem;
}

.w-6\/12{
  width: 50%;
}

.w-7{
  width: 1.75rem;
}

.w-8{
  width: 2rem;
}

.w-9{
  width: 2.25rem;
}

.w-\[0\.9375rem\]{
  width: 0.9375rem;
}

.w-\[1\.125rem\]{
  width: 1.125rem;
}

.w-\[1\.4rem\]{
  width: 1.4rem;
}

.w-\[10px\]{
  width: 10px;
}

.w-\[130px\]{
  width: 130px;
}

.w-\[150px\]{
  width: 150px;
}

.w-\[15px\]{
  width: 15px;
}

.w-\[160px\]{
  width: 160px;
}

.w-\[260px\]{
  width: 260px;
}

.w-\[2px\]{
  width: 2px;
}

.w-\[300px\]{
  width: 300px;
}

.w-\[304px\]{
  width: 304px;
}

.w-\[30px\]{
  width: 30px;
}

.w-\[328px\]{
  width: 328px;
}

.w-\[32px\]{
  width: 32px;
}

.w-\[350px\]{
  width: 350px;
}

.w-\[400px\]{
  width: 400px;
}

.w-\[45\%\]{
  width: 45%;
}

.w-\[4px\]{
  width: 4px;
}

.w-\[50px\]{
  width: 50px;
}

.w-\[6px\]{
  width: 6px;
}

.w-\[70px\]{
  width: 70px;
}

.w-\[72px\]{
  width: 72px;
}

.w-\[76px\]{
  width: 76px;
}

.w-\[calc\(100\%\)\]{
  width: calc(100%);
}

.w-\[calc\(100\%-100px\)\]{
  width: calc(100% - 100px);
}

.w-auto{
  width: auto;
}

.w-full{
  width: 100%;
}

.w-px{
  width: 1px;
}

.w-screen{
  width: 100vw;
}

.min-w-0{
  min-width: 0px;
}

.min-w-10{
  min-width: 2.5rem;
}

.min-w-32{
  min-width: 8rem;
}

.min-w-56{
  min-width: 14rem;
}

.min-w-\[100px\]{
  min-width: 100px;
}

.min-w-\[10px\]{
  min-width: 10px;
}

.min-w-\[310px\]{
  min-width: 310px;
}

.min-w-\[40px\]{
  min-width: 40px;
}

.min-w-\[48px\]{
  min-width: 48px;
}

.min-w-\[64px\]{
  min-width: 64px;
}

.min-w-max{
  min-width: -moz-max-content;
  min-width: max-content;
}

.max-w-\[200px\]{
  max-width: 200px;
}

.max-w-\[267px\]{
  max-width: 267px;
}

.max-w-\[325px\]{
  max-width: 325px;
}

.max-w-\[500px\]{
  max-width: 500px;
}

.max-w-\[90\%\]{
  max-width: 90%;
}

.max-w-\[calc\(100\%-1rem\)\]{
  max-width: calc(100% - 1rem);
}

.max-w-\[calc\(100\%-3rem\)\]{
  max-width: calc(100% - 3rem);
}

.max-w-full{
  max-width: 100%;
}

.flex-1{
  flex: 1 1 0%;
}

.flex-auto{
  flex: 1 1 auto;
}

.flex-shrink{
  flex-shrink: 1;
}

.shrink{
  flex-shrink: 1;
}

.shrink-0{
  flex-shrink: 0;
}

.flex-grow{
  flex-grow: 1;
}

.grow{
  flex-grow: 1;
}

.grow-0{
  flex-grow: 0;
}

.basis-auto{
  flex-basis: auto;
}

.table-auto{
  table-layout: auto;
}

.table-fixed{
  table-layout: fixed;
}

.caption-top{
  caption-side: top;
}

.caption-bottom{
  caption-side: bottom;
}

.border-collapse{
  border-collapse: collapse;
}

.border-separate{
  border-collapse: separate;
}

.origin-\[0_0\]{
  transform-origin: 0 0;
}

.origin-\[50\%_50\%\]{
  transform-origin: 50% 50%;
}

.origin-\[center_bottom_0\]{
  transform-origin: center bottom 0;
}

.origin-bottom{
  transform-origin: bottom;
}

.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-\[6px\]{
  --tw-translate-x: -6px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[150\%\]{
  --tw-translate-x: 150%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[6px\]{
  --tw-translate-y: 6px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45{
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[0\.25\]{
  --tw-scale-x: 0.25;
  --tw-scale-y: 0.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[1\.02\]{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-y-\[0\.8\]{
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-cpu{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu{
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none{
  transform: none;
}

.animate-\[fade-in_0\.15s_both\]{
  animation: fade-in 0.15s both;
}

.animate-\[fade-in_0\.3s_both\]{
  animation: fade-in 0.3s both;
}

.animate-\[fade-in_350ms_ease-in-out\]{
  animation: fade-in 350ms ease-in-out;
}

.animate-\[fade-out_0\.15s_both\]{
  animation: fade-out 0.15s both;
}

.animate-\[fade-out_0\.3s_both\]{
  animation: fade-out 0.3s both;
}

.animate-\[fade-out_350ms_ease-in-out\]{
  animation: fade-out 350ms ease-in-out;
}

.animate-\[progress_3s_ease-in-out_infinite\]{
  animation: progress 3s ease-in-out infinite;
}

.animate-\[show-up-clock_350ms_linear\]{
  animation: show-up-clock 350ms linear;
}

.animate-\[slide-in-left_0\.8s_both\]{
  animation: slide-in-left 0.8s both;
}

.animate-\[slide-in-right_0\.8s_both\]{
  animation: slide-in-right 0.8s both;
}

.animate-\[slide-out-left_0\.8s_both\]{
  animation: slide-out-left 0.8s both;
}

.animate-\[slide-out-right_0\.8s_both\]{
  animation: slide-out-right 0.8s both;
}

.animate-\[spinner-grow_0\.75s_linear_infinite\]{
  animation: spinner-grow 0.75s linear infinite;
}

@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}

.animate-spin{
  animation: spin 1s linear infinite;
}

.cursor-default{
  cursor: default;
}

.cursor-none{
  cursor: none;
}

.cursor-pointer{
  cursor: pointer;
}

.touch-auto{
  touch-action: auto;
}

.touch-none{
  touch-action: none;
}

.touch-pan-x{
  --tw-pan-x: pan-x;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pan-left{
  --tw-pan-x: pan-left;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pan-right{
  --tw-pan-x: pan-right;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pan-y{
  --tw-pan-y: pan-y;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pan-up{
  --tw-pan-y: pan-up;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pan-down{
  --tw-pan-y: pan-down;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-pinch-zoom{
  --tw-pinch-zoom: pinch-zoom;
  touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.touch-manipulation{
  touch-action: manipulation;
}

.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.select-text{
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}

.select-all{
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.select-auto{
  -webkit-user-select: auto;
     -moz-user-select: auto;
          user-select: auto;
}

.resize-none{
  resize: none;
}

.resize-y{
  resize: vertical;
}

.resize-x{
  resize: horizontal;
}

.resize{
  resize: both;
}

.snap-none{
  scroll-snap-type: none;
}

.snap-x{
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}

.snap-y{
  scroll-snap-type: y var(--tw-scroll-snap-strictness);
}

.snap-both{
  scroll-snap-type: both var(--tw-scroll-snap-strictness);
}

.snap-mandatory{
  --tw-scroll-snap-strictness: mandatory;
}

.snap-proximity{
  --tw-scroll-snap-strictness: proximity;
}

.snap-start{
  scroll-snap-align: start;
}

.snap-end{
  scroll-snap-align: end;
}

.snap-center{
  scroll-snap-align: center;
}

.snap-align-none{
  scroll-snap-align: none;
}

.snap-normal{
  scroll-snap-stop: normal;
}

.snap-always{
  scroll-snap-stop: always;
}

.list-inside{
  list-style-position: inside;
}

.list-outside{
  list-style-position: outside;
}

.list-none{
  list-style-type: none;
}

.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.appearance-auto{
  -webkit-appearance: auto;
     -moz-appearance: auto;
          appearance: auto;
}

.break-before-auto{
  -moz-column-break-before: auto;
       break-before: auto;
}

.break-before-avoid{
  -moz-column-break-before: avoid;
       break-before: avoid;
}

.break-before-all{
  -moz-column-break-before: all;
       break-before: all;
}

.break-before-avoid-page{
  -moz-column-break-before: avoid;
       break-before: avoid-page;
}

.break-before-page{
  -moz-column-break-before: page;
       break-before: page;
}

.break-before-left{
  -moz-column-break-before: left;
       break-before: left;
}

.break-before-right{
  -moz-column-break-before: right;
       break-before: right;
}

.break-before-column{
  -moz-column-break-before: column;
       break-before: column;
}

.break-inside-auto{
  -moz-column-break-inside: auto;
       break-inside: auto;
}

.break-inside-avoid{
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}

.break-inside-avoid-page{
  break-inside: avoid-page;
}

.break-inside-avoid-column{
  -moz-column-break-inside: avoid;
       break-inside: avoid-column;
}

.break-after-auto{
  -moz-column-break-after: auto;
       break-after: auto;
}

.break-after-avoid{
  -moz-column-break-after: avoid;
       break-after: avoid;
}

.break-after-all{
  -moz-column-break-after: all;
       break-after: all;
}

.break-after-avoid-page{
  -moz-column-break-after: avoid;
       break-after: avoid-page;
}

.break-after-page{
  -moz-column-break-after: page;
       break-after: page;
}

.break-after-left{
  -moz-column-break-after: left;
       break-after: left;
}

.break-after-right{
  -moz-column-break-after: right;
       break-after: right;
}

.break-after-column{
  -moz-column-break-after: column;
       break-after: column;
}

.grid-flow-row{
  grid-auto-flow: row;
}

.grid-flow-col{
  grid-auto-flow: column;
}

.grid-flow-dense{
  grid-auto-flow: dense;
}

.grid-flow-row-dense{
  grid-auto-flow: row dense;
}

.grid-flow-col-dense{
  grid-auto-flow: column dense;
}

.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.\!flex-row{
  flex-direction: row !important;
}

.flex-row{
  flex-direction: row;
}

.flex-row-reverse{
  flex-direction: row-reverse;
}

.flex-col{
  flex-direction: column;
}

.flex-col-reverse{
  flex-direction: column-reverse;
}

.flex-wrap{
  flex-wrap: wrap;
}

.flex-wrap-reverse{
  flex-wrap: wrap-reverse;
}

.flex-nowrap{
  flex-wrap: nowrap;
}

.place-content-center{
  place-content: center;
}

.place-content-start{
  place-content: start;
}

.place-content-end{
  place-content: end;
}

.place-content-between{
  place-content: space-between;
}

.place-content-around{
  place-content: space-around;
}

.place-content-evenly{
  place-content: space-evenly;
}

.place-content-baseline{
  place-content: baseline;
}

.place-content-stretch{
  place-content: stretch;
}

.place-items-start{
  place-items: start;
}

.place-items-end{
  place-items: end;
}

.place-items-center{
  place-items: center;
}

.place-items-baseline{
  place-items: baseline;
}

.place-items-stretch{
  place-items: stretch;
}

.content-normal{
  align-content: normal;
}

.content-center{
  align-content: center;
}

.content-start{
  align-content: flex-start;
}

.content-end{
  align-content: flex-end;
}

.content-between{
  align-content: space-between;
}

.content-around{
  align-content: space-around;
}

.content-evenly{
  align-content: space-evenly;
}

.content-baseline{
  align-content: baseline;
}

.content-stretch{
  align-content: stretch;
}

.items-start{
  align-items: flex-start;
}

.items-end{
  align-items: flex-end;
}

.items-center{
  align-items: center;
}

.items-baseline{
  align-items: baseline;
}

.items-stretch{
  align-items: stretch;
}

.justify-normal{
  justify-content: normal;
}

.justify-start{
  justify-content: flex-start;
}

.justify-end{
  justify-content: flex-end;
}

.justify-center{
  justify-content: center;
}

.justify-between{
  justify-content: space-between;
}

.justify-around{
  justify-content: space-around;
}

.justify-evenly{
  justify-content: space-evenly;
}

.justify-stretch{
  justify-content: stretch;
}

.justify-items-start{
  justify-items: start;
}

.justify-items-end{
  justify-items: end;
}

.justify-items-center{
  justify-items: center;
}

.justify-items-stretch{
  justify-items: stretch;
}

.\!gap-1{
  gap: 0.25rem !important;
}

.gap-0{
  gap: 0px;
}

.gap-1{
  gap: 0.25rem;
}

.gap-2{
  gap: 0.5rem;
}

.gap-3{
  gap: 0.75rem;
}

.gap-4{
  gap: 1rem;
}

.gap-6{
  gap: 1.5rem;
}

.gap-x-1{
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}

.gap-x-2{
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-3{
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-y-1{
  row-gap: 0.25rem;
}

.gap-y-2{
  row-gap: 0.5rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 1;
}

.space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}

.divide-x > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-y-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 1;
}

.divide-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-divide-x-reverse: 1;
}

.divide-solid > :not([hidden]) ~ :not([hidden]){
  border-style: solid;
}

.divide-dashed > :not([hidden]) ~ :not([hidden]){
  border-style: dashed;
}

.divide-dotted > :not([hidden]) ~ :not([hidden]){
  border-style: dotted;
}

.divide-double > :not([hidden]) ~ :not([hidden]){
  border-style: double;
}

.divide-none > :not([hidden]) ~ :not([hidden]){
  border-style: none;
}

.place-self-auto{
  place-self: auto;
}

.place-self-start{
  place-self: start;
}

.place-self-end{
  place-self: end;
}

.place-self-center{
  place-self: center;
}

.place-self-stretch{
  place-self: stretch;
}

.self-auto{
  align-self: auto;
}

.self-start{
  align-self: flex-start;
}

.self-end{
  align-self: flex-end;
}

.self-center{
  align-self: center;
}

.self-stretch{
  align-self: stretch;
}

.self-baseline{
  align-self: baseline;
}

.justify-self-auto{
  justify-self: auto;
}

.justify-self-start{
  justify-self: start;
}

.justify-self-end{
  justify-self: end;
}

.justify-self-center{
  justify-self: center;
}

.justify-self-stretch{
  justify-self: stretch;
}

.overflow-auto{
  overflow: auto;
}

.\!overflow-hidden{
  overflow: hidden !important;
}

.overflow-hidden{
  overflow: hidden;
}

.overflow-clip{
  overflow: clip;
}

.overflow-visible{
  overflow: visible;
}

.overflow-scroll{
  overflow: scroll;
}

.overflow-x-auto{
  overflow-x: auto;
}

.overflow-y-auto{
  overflow-y: auto;
}

.overflow-x-hidden{
  overflow-x: hidden;
}

.overflow-y-hidden{
  overflow-y: hidden;
}

.overflow-x-clip{
  overflow-x: clip;
}

.overflow-y-clip{
  overflow-y: clip;
}

.overflow-x-visible{
  overflow-x: visible;
}

.overflow-y-visible{
  overflow-y: visible;
}

.overflow-x-scroll{
  overflow-x: scroll;
}

.overflow-y-scroll{
  overflow-y: scroll;
}

.overscroll-auto{
  overscroll-behavior: auto;
}

.overscroll-contain{
  overscroll-behavior: contain;
}

.overscroll-none{
  overscroll-behavior: none;
}

.overscroll-y-auto{
  overscroll-behavior-y: auto;
}

.overscroll-y-contain{
  overscroll-behavior-y: contain;
}

.overscroll-y-none{
  overscroll-behavior-y: none;
}

.overscroll-x-auto{
  overscroll-behavior-x: auto;
}

.overscroll-x-contain{
  overscroll-behavior-x: contain;
}

.overscroll-x-none{
  overscroll-behavior-x: none;
}

.scroll-auto{
  scroll-behavior: auto;
}

.scroll-smooth{
  scroll-behavior: smooth;
}

.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overflow-ellipsis{
  text-overflow: ellipsis;
}

.text-ellipsis{
  text-overflow: ellipsis;
}

.text-clip{
  text-overflow: clip;
}

.hyphens-none{
  -webkit-hyphens: none;
          hyphens: none;
}

.hyphens-manual{
  -webkit-hyphens: manual;
          hyphens: manual;
}

.hyphens-auto{
  -webkit-hyphens: auto;
          hyphens: auto;
}

.whitespace-normal{
  white-space: normal;
}

.\!whitespace-nowrap{
  white-space: nowrap !important;
}

.whitespace-nowrap{
  white-space: nowrap;
}

.whitespace-pre{
  white-space: pre;
}

.whitespace-pre-line{
  white-space: pre-line;
}

.whitespace-pre-wrap{
  white-space: pre-wrap;
}

.whitespace-break-spaces{
  white-space: break-spaces;
}

.text-wrap{
  text-wrap: wrap;
}

.text-nowrap{
  text-wrap: nowrap;
}

.text-balance{
  text-wrap: balance;
}

.text-pretty{
  text-wrap: pretty;
}

.break-normal{
  overflow-wrap: normal;
  word-break: normal;
}

.break-words{
  overflow-wrap: break-word;
}

.break-all{
  word-break: break-all;
}

.break-keep{
  word-break: keep-all;
}

.rounded{
  border-radius: 0.25rem;
}

.rounded-\[0\.25rem\]{
  border-radius: 0.25rem;
}

.rounded-\[0\.5rem\]{
  border-radius: 0.5rem;
}

.rounded-\[0\.6rem\]{
  border-radius: 0.6rem;
}

.rounded-\[100\%\]{
  border-radius: 100%;
}

.rounded-\[10px\]{
  border-radius: 10px;
}

.rounded-\[16px\]{
  border-radius: 16px;
}

.rounded-\[50\%\]{
  border-radius: 50%;
}

.rounded-\[999px\]{
  border-radius: 999px;
}

.rounded-full{
  border-radius: 9999px;
}

.rounded-lg{
  border-radius: 0.5rem;
}

.rounded-md{
  border-radius: 0.375rem;
}

.rounded-xl{
  border-radius: 0.75rem;
}

.rounded-b{
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-b-lg{
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-b-xl{
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

.rounded-e{
  border-start-end-radius: 0.25rem;
  border-end-end-radius: 0.25rem;
}

.rounded-l{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-l-\[0\.25rem\]{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-r{
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rounded-r-\[0\.25rem\]{
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rounded-s{
  border-start-start-radius: 0.25rem;
  border-end-start-radius: 0.25rem;
}

.rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.rounded-t-\[0\.6rem\]{
  border-top-left-radius: 0.6rem;
  border-top-right-radius: 0.6rem;
}

.rounded-t-lg{
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-t-xl{
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.rounded-bl{
  border-bottom-left-radius: 0.25rem;
}

.rounded-bl-none{
  border-bottom-left-radius: 0px;
}

.rounded-br{
  border-bottom-right-radius: 0.25rem;
}

.rounded-br-2xl{
  border-bottom-right-radius: 1rem;
}

.rounded-ee{
  border-end-end-radius: 0.25rem;
}

.rounded-es{
  border-end-start-radius: 0.25rem;
}

.rounded-se{
  border-start-end-radius: 0.25rem;
}

.rounded-ss{
  border-start-start-radius: 0.25rem;
}

.rounded-tl{
  border-top-left-radius: 0.25rem;
}

.rounded-tl-2xl{
  border-top-left-radius: 1rem;
}

.rounded-tr{
  border-top-right-radius: 0.25rem;
}

.rounded-tr-2xl{
  border-top-right-radius: 1rem;
}

.\!border-0{
  border-width: 0px !important;
}

.\!border-\[3px\]{
  border-width: 3px !important;
}

.border{
  border-width: 1px;
}

.border-0{
  border-width: 0px;
}

.border-2{
  border-width: 2px;
}

.border-4{
  border-width: 4px;
}

.border-\[\.125rem\]{
  border-width: .125rem;
}

.border-\[0\.125rem\]{
  border-width: 0.125rem;
}

.border-\[0\.15em\]{
  border-width: 0.15em;
}

.border-\[14px\]{
  border-width: 14px;
}

.border-\[1px\]{
  border-width: 1px;
}

.border-x{
  border-left-width: 1px;
  border-right-width: 1px;
}

.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b{
  border-bottom-width: 1px;
}

.border-b-2{
  border-bottom-width: 2px;
}

.border-e{
  border-inline-end-width: 1px;
}

.border-l{
  border-left-width: 1px;
}

.border-l-0{
  border-left-width: 0px;
}

.border-l-\[0\.125rem\]{
  border-left-width: 0.125rem;
}

.border-r{
  border-right-width: 1px;
}

.border-r-0{
  border-right-width: 0px;
}

.border-s{
  border-inline-start-width: 1px;
}

.border-t{
  border-top-width: 1px;
}

.border-t-0{
  border-top-width: 0px;
}

.\!border-solid{
  border-style: solid !important;
}

.border-solid{
  border-style: solid;
}

.border-dashed{
  border-style: dashed;
}

.border-dotted{
  border-style: dotted;
}

.border-double{
  border-style: double;
}

.border-hidden{
  border-style: hidden;
}

.border-none{
  border-style: none;
}

.\!border-\[\#14a44d\]{
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.\!border-\[\#b2b3b4\]{
  --tw-border-opacity: 1 !important;
  border-color: rgb(178 179 180 / var(--tw-border-opacity, 1)) !important;
}

.\!border-\[\#dc4c64\]{
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.\!border-black{
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.\!border-gray-400{
  --tw-border-opacity: 1 !important;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1)) !important;
}

.border-\[\#14a44d\]{
  --tw-border-opacity: 1;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
}

.border-\[\#3b71ca\]{
  --tw-border-opacity: 1;
  border-color: rgb(59 113 202 / var(--tw-border-opacity, 1));
}

.border-\[\#dc4c64\]{
  --tw-border-opacity: 1;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
}

.border-\[\#eee\]{
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity, 1));
}

.border-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-current{
  border-color: currentColor;
}

.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-400{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.border-neutral-100{
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}

.border-neutral-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));
}

.border-neutral-300{
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}

.border-transparent{
  border-color: transparent;
}

.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-r-transparent{
  border-right-color: transparent;
}

.border-t-transparent{
  border-top-color: transparent;
}

.border-opacity-10{
  --tw-border-opacity: 0.1;
}

.\!bg-\[\#858585\]{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(133 133 133 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-neutral-100{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1)) !important;
}

.\!bg-zinc-500{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#00000012\]{
  background-color: #00000012;
}

.bg-\[\#00000066\]{
  background-color: #00000066;
}

.bg-\[\#000000e6\]{
  background-color: #000000e6;
}

.bg-\[\#3b71ca\]{
  --tw-bg-opacity: 1;
  background-color: rgb(59 113 202 / var(--tw-bg-opacity, 1));
}

.bg-\[\#6d6d6d\]{
  --tw-bg-opacity: 1;
  background-color: rgb(109 109 109 / var(--tw-bg-opacity, 1));
}

.bg-\[\#aaa\]{
  --tw-bg-opacity: 1;
  background-color: rgb(170 170 170 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eceff1\]{
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.bg-\[\#eee\]{
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1));
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.4\)\]{
  background-color: rgba(0,0,0,0.4);
}

.bg-amber-200{
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}

.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/10{
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/40{
  background-color: rgb(0 0 0 / 0.4);
}

.bg-blue-200{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-500\/20{
  background-color: rgb(59 130 246 / 0.2);
}

.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-current{
  background-color: currentColor;
}

.bg-cyan-200{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));
}

.bg-cyan-600{
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));
}

.bg-emerald-200{
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}

.bg-emerald-600{
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));
}

.bg-fuchsia-200{
  --tw-bg-opacity: 1;
  background-color: rgb(245 208 254 / var(--tw-bg-opacity, 1));
}

.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-600{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.bg-green-200{
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}

.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-indigo-200{
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));
}

.bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-inherit{
  background-color: inherit;
}

.bg-lime-200{
  --tw-bg-opacity: 1;
  background-color: rgb(217 249 157 / var(--tw-bg-opacity, 1));
}

.bg-lime-600{
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity, 1));
}

.bg-neutral-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.bg-neutral-600{
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
}

.bg-neutral-700{
  --tw-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
}

.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.bg-orange-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}

.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-pink-200{
  --tw-bg-opacity: 1;
  background-color: rgb(251 207 232 / var(--tw-bg-opacity, 1));
}

.bg-purple-200{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-rose-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 205 211 / var(--tw-bg-opacity, 1));
}

.bg-sky-200{
  --tw-bg-opacity: 1;
  background-color: rgb(186 230 253 / var(--tw-bg-opacity, 1));
}

.bg-slate-200{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}

.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}

.bg-stone-200{
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity, 1));
}

.bg-teal-200{
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity, 1));
}

.bg-teal-400{
  --tw-bg-opacity: 1;
  background-color: rgb(45 212 191 / var(--tw-bg-opacity, 1));
}

.bg-teal-500{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.bg-teal-700{
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}

.bg-transparent{
  background-color: transparent;
}

.bg-violet-200{
  --tw-bg-opacity: 1;
  background-color: rgb(221 214 254 / var(--tw-bg-opacity, 1));
}

.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/30{
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/80{
  background-color: rgb(255 255 255 / 0.8);
}

.bg-zinc-200{
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));
}

.bg-zinc-500{
  --tw-bg-opacity: 1;
  background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
}

.bg-zinc-600{
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

.bg-zinc-600\/50{
  background-color: rgb(82 82 91 / 0.5);
}

.bg-zinc-700{
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}

.bg-opacity-75{
  --tw-bg-opacity: 0.75;
}

.decoration-slice{
  -webkit-box-decoration-break: slice;
          box-decoration-break: slice;
}

.decoration-clone{
  -webkit-box-decoration-break: clone;
          box-decoration-break: clone;
}

.box-decoration-slice{
  -webkit-box-decoration-break: slice;
          box-decoration-break: slice;
}

.box-decoration-clone{
  -webkit-box-decoration-break: clone;
          box-decoration-break: clone;
}

.bg-cover{
  background-size: cover;
}

.bg-fixed{
  background-attachment: fixed;
}

.bg-local{
  background-attachment: local;
}

.bg-scroll{
  background-attachment: scroll;
}

.bg-clip-border{
  background-clip: border-box;
}

.bg-clip-padding{
  background-clip: padding-box;
}

.bg-clip-content{
  background-clip: content-box;
}

.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}

.bg-center{
  background-position: center;
}

.bg-repeat{
  background-repeat: repeat;
}

.bg-no-repeat{
  background-repeat: no-repeat;
}

.bg-repeat-x{
  background-repeat: repeat-x;
}

.bg-repeat-y{
  background-repeat: repeat-y;
}

.bg-repeat-round{
  background-repeat: round;
}

.bg-repeat-space{
  background-repeat: space;
}

.bg-origin-border{
  background-origin: border-box;
}

.bg-origin-padding{
  background-origin: padding-box;
}

.bg-origin-content{
  background-origin: content-box;
}

.fill-\[\#afafaf\]{
  fill: #afafaf;
}

.fill-current{
  fill: currentColor;
}

.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}

.object-fill{
  -o-object-fit: fill;
     object-fit: fill;
}

.object-none{
  -o-object-fit: none;
     object-fit: none;
}

.object-scale-down{
  -o-object-fit: scale-down;
     object-fit: scale-down;
}

.\!p-0{
  padding: 0px !important;
}

.p-0{
  padding: 0px;
}

.p-1{
  padding: 0.25rem;
}

.p-10{
  padding: 2.5rem;
}

.p-2{
  padding: 0.5rem;
}

.p-2\.5{
  padding: 0.625rem;
}

.p-3{
  padding: 0.75rem;
}

.p-4{
  padding: 1rem;
}

.p-\[1rem\]{
  padding: 1rem;
}

.p-\[5px\]{
  padding: 5px;
}

.p-\[auto\]{
  padding: auto;
}

.\!py-0{
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.\!py-2{
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.px-0{
  padding-left: 0px;
  padding-right: 0px;
}

.px-0\.5{
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-\[0\.4rem\]{
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}

.px-\[1\.4rem\]{
  padding-left: 1.4rem;
  padding-right: 1.4rem;
}

.px-\[10px\]{
  padding-left: 10px;
  padding-right: 10px;
}

.px-\[12px\]{
  padding-left: 12px;
  padding-right: 12px;
}

.px-\[auto\]{
  padding-left: auto;
  padding-right: auto;
}

.py-0{
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-\[0\.32rem\]{
  padding-top: 0.32rem;
  padding-bottom: 0.32rem;
}

.py-\[0\.33rem\]{
  padding-top: 0.33rem;
  padding-bottom: 0.33rem;
}

.py-\[0\.4375rem\]{
  padding-top: 0.4375rem;
  padding-bottom: 0.4375rem;
}

.py-\[0\.4rem\]{
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
}

.py-\[10px\]{
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[1px\]{
  padding-top: 1px;
  padding-bottom: 1px;
}

.py-\[5px\]{
  padding-top: 5px;
  padding-bottom: 5px;
}

.pb-0{
  padding-bottom: 0px;
}

.pb-3{
  padding-bottom: 0.75rem;
}

.pb-\[5px\]{
  padding-bottom: 5px;
}

.pl-0{
  padding-left: 0px;
}

.pl-1{
  padding-left: 0.25rem;
}

.pl-2{
  padding-left: 0.5rem;
}

.pl-4{
  padding-left: 1rem;
}

.pl-\[1\.5rem\]{
  padding-left: 1.5rem;
}

.pl-\[18px\]{
  padding-left: 18px;
}

.pl-\[50px\]{
  padding-left: 50px;
}

.pl-\[8px\]{
  padding-left: 8px;
}

.pr-1{
  padding-right: 0.25rem;
}

.pr-2{
  padding-right: 0.5rem;
}

.pr-3{
  padding-right: 0.75rem;
}

.pr-\[24px\]{
  padding-right: 24px;
}

.ps-1{
  padding-inline-start: 0.25rem;
}

.pt-1{
  padding-top: 0.25rem;
}

.pt-2{
  padding-top: 0.5rem;
}

.pt-2\.5{
  padding-top: 0.625rem;
}

.pt-3{
  padding-top: 0.75rem;
}

.pt-\[0\.37rem\]{
  padding-top: 0.37rem;
}

.pt-\[6px\]{
  padding-top: 6px;
}

.text-left{
  text-align: left;
}

.text-center{
  text-align: center;
}

.text-right{
  text-align: right;
}

.text-justify{
  text-align: justify;
}

.text-start{
  text-align: start;
}

.text-end{
  text-align: end;
}

.align-baseline{
  vertical-align: baseline;
}

.align-top{
  vertical-align: top;
}

.align-middle{
  vertical-align: middle;
}

.align-bottom{
  vertical-align: bottom;
}

.align-text-top{
  vertical-align: text-top;
}

.align-text-bottom{
  vertical-align: text-bottom;
}

.align-sub{
  vertical-align: sub;
}

.align-super{
  vertical-align: super;
}

.align-\[-0\.125em\]{
  vertical-align: -0.125em;
}

.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.\!text-3xl{
  font-size: 1.875rem !important;
  line-height: 2.25rem !important;
}

.text-\[0\.8rem\]{
  font-size: 0.8rem;
}

.text-\[0\.9rem\]{
  font-size: 0.9rem;
}

.text-\[1\.1rem\]{
  font-size: 1.1rem;
}

.text-\[10px\]{
  font-size: 10px;
}

.text-\[12px\]{
  font-size: 12px;
}

.text-\[13px\]{
  font-size: 13px;
}

.text-\[14px\]{
  font-size: 14px;
}

.text-\[16px\]{
  font-size: 16px;
}

.text-\[18pt\]{
  font-size: 18pt;
}

.text-\[18px\]{
  font-size: 18px;
}

.text-\[2\.5rem\]{
  font-size: 2.5rem;
}

.text-\[20px\]{
  font-size: 20px;
}

.text-\[22px\]{
  font-size: 22px;
}

.text-\[28pt\]{
  font-size: 28pt;
}

.text-\[3\.75rem\]{
  font-size: 3.75rem;
}

.text-\[34px\]{
  font-size: 34px;
}

.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-black{
  font-weight: 900;
}

.font-bold{
  font-weight: 700;
}

.font-light{
  font-weight: 300;
}

.font-medium{
  font-weight: 500;
}

.font-normal{
  font-weight: 400;
}

.font-semibold{
  font-weight: 600;
}

.uppercase{
  text-transform: uppercase;
}

.lowercase{
  text-transform: lowercase;
}

.capitalize{
  text-transform: capitalize;
}

.normal-case{
  text-transform: none;
}

.italic{
  font-style: italic;
}

.not-italic{
  font-style: normal;
}

.normal-nums{
  font-variant-numeric: normal;
}

.ordinal{
  --tw-ordinal: ordinal;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.slashed-zero{
  --tw-slashed-zero: slashed-zero;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.lining-nums{
  --tw-numeric-figure: lining-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.oldstyle-nums{
  --tw-numeric-figure: oldstyle-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.proportional-nums{
  --tw-numeric-spacing: proportional-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.tabular-nums{
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.diagonal-fractions{
  --tw-numeric-fraction: diagonal-fractions;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.stacked-fractions{
  --tw-numeric-fraction: stacked-fractions;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-10{
  line-height: 2.5rem;
}

.leading-6{
  line-height: 1.5rem;
}

.leading-9{
  line-height: 2.25rem;
}

.leading-\[1\.2\]{
  line-height: 1.2;
}

.leading-\[1\.5\]{
  line-height: 1.5;
}

.leading-\[1\.6\]{
  line-height: 1.6;
}

.leading-\[2\.15\]{
  line-height: 2.15;
}

.leading-\[40px\]{
  line-height: 40px;
}

.leading-loose{
  line-height: 2;
}

.leading-normal{
  line-height: 1.5;
}

.tracking-\[-0\.00833em\]{
  letter-spacing: -0.00833em;
}

.tracking-\[\.1rem\]{
  letter-spacing: .1rem;
}

.tracking-\[0\.1rem\]{
  letter-spacing: 0.1rem;
}

.tracking-\[1\.7px\]{
  letter-spacing: 1.7px;
}

.tracking-normal{
  letter-spacing: 0em;
}

.\!text-\[\#14a44d\]{
  --tw-text-opacity: 1 !important;
  color: rgb(20 164 77 / var(--tw-text-opacity, 1)) !important;
}

.\!text-\[\#dc4c64\]{
  --tw-text-opacity: 1 !important;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1)) !important;
}

.\!text-amber-700{
  --tw-text-opacity: 1 !important;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1)) !important;
}

.\!text-blue-500{
  --tw-text-opacity: 1 !important;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-50{
  --tw-text-opacity: 1 !important;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1)) !important;
}

.\!text-gray-500{
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1)) !important;
}

.\!text-green-600{
  --tw-text-opacity: 1 !important;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1)) !important;
}

.\!text-red-500{
  --tw-text-opacity: 1 !important;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) !important;
}

.\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.\!text-zinc-500{
  --tw-text-opacity: 1 !important;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1)) !important;
}

.\!text-zinc-900{
  --tw-text-opacity: 1 !important;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#14a44d\]{
  --tw-text-opacity: 1;
  color: rgb(20 164 77 / var(--tw-text-opacity, 1));
}

.text-\[\#212529\]{
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity, 1));
}

.text-\[\#3b71ca\]{
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.text-\[\#4f4f4f\]{
  --tw-text-opacity: 1;
  color: rgb(79 79 79 / var(--tw-text-opacity, 1));
}

.text-\[\#b3afaf\]{
  --tw-text-opacity: 1;
  color: rgb(179 175 175 / var(--tw-text-opacity, 1));
}

.text-\[\#b3b3b3\]{
  --tw-text-opacity: 1;
  color: rgb(179 179 179 / var(--tw-text-opacity, 1));
}

.text-\[\#dc4c64\]{
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.text-\[\#ffffff8a\]{
  color: #ffffff8a;
}

.text-\[rgb\(220\2c 76\2c 100\)\]{
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-black\/50{
  color: rgb(0 0 0 / 0.5);
}

.text-black\/\[64\]{
  color: rgb(0 0 0 / 64);
}

.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-50{
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}

.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-neutral-200{
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity, 1));
}

.text-neutral-400{
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}

.text-neutral-50{
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.text-neutral-500{
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.text-neutral-600{
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}

.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-sky-600{
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}

.text-slate-100{
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}

.text-teal-600{
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}

.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.underline{
  text-decoration-line: underline;
}

.overline{
  text-decoration-line: overline;
}

.line-through{
  text-decoration-line: line-through;
}

.no-underline{
  text-decoration-line: none;
}

.decoration-solid{
  text-decoration-style: solid;
}

.decoration-double{
  text-decoration-style: double;
}

.decoration-dotted{
  text-decoration-style: dotted;
}

.decoration-dashed{
  text-decoration-style: dashed;
}

.decoration-wavy{
  text-decoration-style: wavy;
}

.underline-offset-auto{
  text-underline-offset: auto;
}

.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased{
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

.\!opacity-0{
  opacity: 0 !important;
}

.\!opacity-100{
  opacity: 1 !important;
}

.opacity-0{
  opacity: 0;
}

.opacity-10{
  opacity: 0.1;
}

.opacity-100{
  opacity: 1;
}

.opacity-50{
  opacity: 0.5;
}

.opacity-70{
  opacity: 0.7;
}

.opacity-\[\.53\]{
  opacity: .53;
}

.opacity-\[\.54\]{
  opacity: .54;
}

.bg-blend-normal{
  background-blend-mode: normal;
}

.bg-blend-multiply{
  background-blend-mode: multiply;
}

.bg-blend-screen{
  background-blend-mode: screen;
}

.bg-blend-overlay{
  background-blend-mode: overlay;
}

.bg-blend-darken{
  background-blend-mode: darken;
}

.bg-blend-lighten{
  background-blend-mode: lighten;
}

.bg-blend-color-dodge{
  background-blend-mode: color-dodge;
}

.bg-blend-color-burn{
  background-blend-mode: color-burn;
}

.bg-blend-hard-light{
  background-blend-mode: hard-light;
}

.bg-blend-soft-light{
  background-blend-mode: soft-light;
}

.bg-blend-difference{
  background-blend-mode: difference;
}

.bg-blend-exclusion{
  background-blend-mode: exclusion;
}

.bg-blend-hue{
  background-blend-mode: hue;
}

.bg-blend-saturation{
  background-blend-mode: saturation;
}

.bg-blend-color{
  background-blend-mode: color;
}

.bg-blend-luminosity{
  background-blend-mode: luminosity;
}

.mix-blend-normal{
  mix-blend-mode: normal;
}

.mix-blend-multiply{
  mix-blend-mode: multiply;
}

.mix-blend-screen{
  mix-blend-mode: screen;
}

.mix-blend-overlay{
  mix-blend-mode: overlay;
}

.mix-blend-darken{
  mix-blend-mode: darken;
}

.mix-blend-lighten{
  mix-blend-mode: lighten;
}

.mix-blend-color-dodge{
  mix-blend-mode: color-dodge;
}

.mix-blend-color-burn{
  mix-blend-mode: color-burn;
}

.mix-blend-hard-light{
  mix-blend-mode: hard-light;
}

.mix-blend-soft-light{
  mix-blend-mode: soft-light;
}

.mix-blend-difference{
  mix-blend-mode: difference;
}

.mix-blend-exclusion{
  mix-blend-mode: exclusion;
}

.mix-blend-hue{
  mix-blend-mode: hue;
}

.mix-blend-saturation{
  mix-blend-mode: saturation;
}

.mix-blend-color{
  mix-blend-mode: color;
}

.mix-blend-luminosity{
  mix-blend-mode: luminosity;
}

.mix-blend-plus-darker{
  mix-blend-mode: plus-darker;
}

.mix-blend-plus-lighter{
  mix-blend-mode: plus-lighter;
}

.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0px_3px_0_rgba\(0\2c 0\2c 0\2c 0\.07\)\2c 0_2px_2px_0_rgba\(0\2c 0\2c 0\2c 0\.04\)\]{
  --tw-shadow: 0 0px 3px 0 rgba(0,0,0,0.07),0 2px 2px 0 rgba(0,0,0,0.04);
  --tw-shadow-colored: 0 0px 3px 0 var(--tw-shadow-color), 0 2px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_10px_15px_-3px_rgba\(0\2c 0\2c 0\2c 0\.07\)\2c 0_4px_6px_-2px_rgba\(0\2c 0\2c 0\2c 0\.05\)\]{
  --tw-shadow: 0 10px 15px -3px rgba(0,0,0,0.07),0 4px 6px -2px rgba(0,0,0,0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_2px_5px_0_rgba\(0\2c 0\2c 0\2c 0\.16\)\2c _0_2px_10px_0_rgba\(0\2c 0\2c 0\2c 0\.12\)\]{
  --tw-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
  --tw-shadow-colored: 0 2px 5px 0 var(--tw-shadow-color), 0 2px 10px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_4px_9px_-4px_\#3b71ca\]{
  --tw-shadow: 0 4px 9px -4px #3b71ca;
  --tw-shadow-colored: 0 4px 9px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_2px_15px_-3px_rgba\(0\2c 0\2c 0\2c \.07\)\2c _0px_10px_20px_-2px_rgba\(0\2c 0\2c 0\2c \.04\)\]{
  --tw-shadow: 0px 2px 15px -3px rgba(0,0,0,.07), 0px 10px 20px -2px rgba(0,0,0,.04);
  --tw-shadow-colored: 0px 2px 15px -3px var(--tw-shadow-color), 0px 10px 20px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline{
  outline-style: solid;
}

.outline-dashed{
  outline-style: dashed;
}

.outline-dotted{
  outline-style: dotted;
}

.outline-double{
  outline-style: double;
}

.ring-inset{
  --tw-ring-inset: inset;
}

.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow{
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale{
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert{
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.sepia{
  --tw-sepia: sepia(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter-none{
  filter: none;
}

.backdrop-blur{
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-grayscale{
  --tw-backdrop-grayscale: grayscale(100%);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-invert{
  --tw-backdrop-invert: invert(100%);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-sepia{
  --tw-backdrop-sepia: sepia(100%);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter{
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter-none{
  -webkit-backdrop-filter: none;
  backdrop-filter: none;
}

.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[background-color\2c _opacity\]{
  transition-property: background-color, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[background-color\2c box-shadow\2c border\]{
  transition-property: background-color,box-shadow,border;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[height\]{
  transition-property: height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[opacity\]{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[transform\2c _opacity\]{
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[transform\2c height\]{
  transition-property: transform,height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[width\]{
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-\[0ms\]{
  transition-delay: 0ms;
}

.duration-100{
  transition-duration: 100ms;
}

.duration-150{
  transition-duration: 150ms;
}

.duration-200{
  transition-duration: 200ms;
}

.duration-300{
  transition-duration: 300ms;
}

.duration-\[1000ms\]{
  transition-duration: 1000ms;
}

.duration-\[150ms\]{
  transition-duration: 150ms;
}

.duration-\[200ms\]{
  transition-duration: 200ms;
}

.duration-\[250ms\]{
  transition-duration: 250ms;
}

.duration-\[350ms\]{
  transition-duration: 350ms;
}

.duration-\[400ms\]{
  transition-duration: 400ms;
}

.ease-\[cubic-bezier\(0\2c 0\2c 0\.15\2c 1\)\2c _cubic-bezier\(0\2c 0\2c 0\.15\2c 1\)\]{
  transition-timing-function: cubic-bezier(0,0,0.15,1), cubic-bezier(0,0,0.15,1);
}

.ease-\[cubic-bezier\(0\.25\2c 0\.1\2c 0\.25\2c 1\)\]{
  transition-timing-function: cubic-bezier(0.25,0.1,0.25,1);
}

.ease-\[cubic-bezier\(0\.25\2c 0\.1\2c 0\.25\2c 1\.0\)\]{
  transition-timing-function: cubic-bezier(0.25,0.1,0.25,1.0);
}

.ease-\[cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)\]{
  transition-timing-function: cubic-bezier(0.4,0,0.2,1);
}

.ease-\[ease\]{
  transition-timing-function: ease;
}

.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear{
  transition-timing-function: linear;
}

.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.will-change-transform{
  will-change: transform;
}

.contain-none{
  contain: none;
}

.contain-content{
  contain: content;
}

.contain-strict{
  contain: strict;
}

.contain-size{
  --tw-contain-size: size;
  contain: var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style);
}

.contain-inline-size{
  --tw-contain-size: inline-size;
  contain: var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style);
}

.contain-layout{
  --tw-contain-layout: layout;
  contain: var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style);
}

.contain-paint{
  --tw-contain-paint: paint;
  contain: var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style);
}

.contain-style{
  --tw-contain-style: style;
  contain: var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style);
}

.forced-color-adjust-auto{
  forced-color-adjust: auto;
}

.forced-color-adjust-none{
  forced-color-adjust: none;
}

.\!\[clip\:rect\(0\2c 0\2c 0\2c 0\)\]{
  clip: rect(0,0,0,0) !important;
}

.\[clip\:rect\(0\2c 0\2c 0\2c 0\)\]{
  clip: rect(0,0,0,0);
}

.\[direction\:ltr\]{
  direction: ltr;
}

.\[overflow-anchor\:none\]{
  overflow-anchor: none;
}

.\[transition\:background-color_\.2s_linear\2c _height_\.2s_ease-in-out\]{
  transition: background-color .2s linear, height .2s ease-in-out;
}

.\[transition\:background-color_\.2s_linear\2c _width_\.2s_ease-in-out\2c _opacity\]{
  transition: background-color .2s linear, width .2s ease-in-out, opacity;
}

.\[transition\:background-color_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\2c box-shadow_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\2c border_250ms_cubic-bezier\(0\.4\2c 0\2c 0\.2\2c 1\)_0ms\]{
  transition: background-color 250ms cubic-bezier(0.4,0,0.2,1) 0ms,box-shadow 250ms cubic-bezier(0.4,0,0.2,1) 0ms,border 250ms cubic-bezier(0.4,0,0.2,1) 0ms;
}

.selection\:bg-transparent *::-moz-selection{
  background-color: transparent;
}

.selection\:bg-transparent *::selection{
  background-color: transparent;
}

.selection\:bg-transparent::-moz-selection{
  background-color: transparent;
}

.selection\:bg-transparent::selection{
  background-color: transparent;
}

.before\:pointer-events-none::before{
  content: var(--tw-content);
  pointer-events: none;
}

.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}

.before\:h-\[0\.875rem\]::before{
  content: var(--tw-content);
  height: 0.875rem;
}

.before\:w-\[0\.875rem\]::before{
  content: var(--tw-content);
  width: 0.875rem;
}

.before\:scale-0::before{
  content: var(--tw-content);
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before{
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:bg-transparent::before{
  content: var(--tw-content);
  background-color: transparent;
}

.before\:opacity-0::before{
  content: var(--tw-content);
  opacity: 0;
}

.before\:shadow-\[0px_0px_0px_13px_transparent\]::before{
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px transparent;
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:content-\[\'\'\]::before{
  --tw-content: '';
  content: var(--tw-content);
}

.checked\:\!border-\[\#14a44d\]:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.checked\:\!border-\[\#dc4c64\]:checked{
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.checked\:\!bg-\[\#14a44d\]:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(20 164 77 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:\!bg-\[\#dc4c64\]:checked{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 76 100 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:before\:opacity-\[0\.16\]:checked::before{
  content: var(--tw-content);
  opacity: 0.16;
}

.checked\:after\:absolute:checked::after{
  content: var(--tw-content);
  position: absolute;
}

.checked\:after\:-mt-px:checked::after{
  content: var(--tw-content);
  margin-top: -1px;
}

.checked\:after\:ml-\[0\.25rem\]:checked::after{
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.checked\:after\:block:checked::after{
  content: var(--tw-content);
  display: block;
}

.checked\:after\:h-\[0\.8125rem\]:checked::after{
  content: var(--tw-content);
  height: 0.8125rem;
}

.checked\:after\:w-\[0\.375rem\]:checked::after{
  content: var(--tw-content);
  width: 0.375rem;
}

.checked\:after\:rotate-45:checked::after{
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:after\:border-\[0\.125rem\]:checked::after{
  content: var(--tw-content);
  border-width: 0.125rem;
}

.checked\:after\:border-l-0:checked::after{
  content: var(--tw-content);
  border-left-width: 0px;
}

.checked\:after\:border-t-0:checked::after{
  content: var(--tw-content);
  border-top-width: 0px;
}

.checked\:after\:border-solid:checked::after{
  content: var(--tw-content);
  border-style: solid;
}

.checked\:after\:border-white:checked::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.checked\:after\:\!bg-\[\#14a44d\]:checked::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1 !important;
  background-color: rgb(20 164 77 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:after\:\!bg-\[\#dc4c64\]:checked::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 76 100 / var(--tw-bg-opacity, 1)) !important;
}

.checked\:after\:bg-transparent:checked::after{
  content: var(--tw-content);
  background-color: transparent;
}

.checked\:after\:content-\[\'\'\]:checked::after{
  --tw-content: '';
  content: var(--tw-content);
}

.empty\:hidden:empty{
  display: none;
}

.hover\:h-auto:hover{
  height: auto;
}

.hover\:w-full:hover{
  width: 100%;
}

.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover{
  cursor: pointer;
}

.hover\:rounded-\[50\%\]:hover{
  border-radius: 50%;
}

.hover\:border:hover{
  border-width: 1px;
}

.hover\:\!bg-\[\#eee\]:hover{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.hover\:bg-\[\#00000014\]:hover{
  background-color: #00000014;
}

.hover\:bg-\[\#00000026\]:hover{
  background-color: #00000026;
}

.hover\:bg-\[unset\]:hover{
  background-color: unset;
}

.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:fill-\[\#8b8b8b\]:hover{
  fill: #8b8b8b;
}

.hover\:object-contain:hover{
  -o-object-fit: contain;
     object-fit: contain;
}

.hover\:text-\[\#3b71ca\]:hover{
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.hover\:text-\[\#8b8b8b\]:hover{
  --tw-text-opacity: 1;
  color: rgb(139 139 139 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-red-600:hover{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:\!opacity-90:hover{
  opacity: 0.9 !important;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:opacity-50:hover{
  opacity: 0.5;
}

.hover\:\!shadow-none:hover{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.hover\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:hover{
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:outline-none:hover{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.hover\:ease-in-out:hover{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.hover\:before\:opacity-\[0\.04\]:hover::before{
  content: var(--tw-content);
  opacity: 0.04;
}

.hover\:before\:shadow-\[0px_0px_0px_13px_rgba\(0\2c 0\2c 0\2c 0\.6\)\]:hover::before{
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px rgba(0,0,0,0.6);
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:rounded-\[50\%\]:focus{
  border-radius: 50%;
}

.focus\:\!border-\[\#14a44d\]:focus{
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!border-\[\#dc4c64\]:focus{
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!bg-\[\#eee\]:focus{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.focus\:bg-\[\#00000014\]:focus{
  background-color: #00000014;
}

.focus\:bg-\[\#00000026\]:focus{
  background-color: #00000026;
}

.focus\:bg-neutral-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.focus\:text-\[\#3b71ca\]:focus{
  --tw-text-opacity: 1;
  color: rgb(59 113 202 / var(--tw-text-opacity, 1));
}

.focus\:text-gray-700:focus{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.focus\:text-white:focus{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:\!opacity-90:focus{
  opacity: 0.9 !important;
}

.focus\:\!shadow-\[inset_0_0_0_1px_\#14a44d\]:focus{
  --tw-shadow: inset 0 0 0 1px #14a44d !important;
  --tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus\:\!shadow-\[inset_0_0_0_1px_\#dc4c64\]:focus{
  --tw-shadow: inset 0 0 0 1px #dc4c64 !important;
  --tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:focus{
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-none:focus{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:transition-\[border-color_0\.2s\]:focus{
  transition-property: border-color 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.focus\:placeholder\:opacity-100:focus::-moz-placeholder{
  opacity: 1;
}

.focus\:placeholder\:opacity-100:focus::placeholder{
  opacity: 1;
}

.focus\:before\:scale-100:focus::before{
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:before\:opacity-\[0\.12\]:focus::before{
  content: var(--tw-content);
  opacity: 0.12;
}

.focus\:before\:shadow-\[0px_0px_0px_13px_rgba\(0\2c 0\2c 0\2c 0\.6\)\]:focus::before{
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px rgba(0,0,0,0.6);
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:before\:transition-\[box-shadow_0\.2s\2c transform_0\.2s\]:focus::before{
  content: var(--tw-content);
  transition-property: box-shadow 0.2s,transform 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.focus\:after\:absolute:focus::after{
  content: var(--tw-content);
  position: absolute;
}

.focus\:after\:z-\[1\]:focus::after{
  content: var(--tw-content);
  z-index: 1;
}

.focus\:after\:block:focus::after{
  content: var(--tw-content);
  display: block;
}

.focus\:after\:h-\[0\.875rem\]:focus::after{
  content: var(--tw-content);
  height: 0.875rem;
}

.focus\:after\:w-\[0\.875rem\]:focus::after{
  content: var(--tw-content);
  width: 0.875rem;
}

.focus\:after\:rounded-\[0\.125rem\]:focus::after{
  content: var(--tw-content);
  border-radius: 0.125rem;
}

.focus\:after\:content-\[\'\'\]:focus::after{
  --tw-content: '';
  content: var(--tw-content);
}

.checked\:focus\:before\:scale-100:focus:checked::before{
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:focus\:before\:shadow-\[0px_0px_0px_13px_\#3b71ca\]:focus:checked::before{
  content: var(--tw-content);
  --tw-shadow: 0px 0px 0px 13px #3b71ca;
  --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.checked\:focus\:before\:transition-\[box-shadow_0\.2s\2c transform_0\.2s\]:focus:checked::before{
  content: var(--tw-content);
  transition-property: box-shadow 0.2s,transform 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.checked\:focus\:after\:-mt-px:focus:checked::after{
  content: var(--tw-content);
  margin-top: -1px;
}

.checked\:focus\:after\:ml-\[0\.25rem\]:focus:checked::after{
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.checked\:focus\:after\:h-\[0\.8125rem\]:focus:checked::after{
  content: var(--tw-content);
  height: 0.8125rem;
}

.checked\:focus\:after\:w-\[0\.375rem\]:focus:checked::after{
  content: var(--tw-content);
  width: 0.375rem;
}

.checked\:focus\:after\:rotate-45:focus:checked::after{
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checked\:focus\:after\:rounded-none:focus:checked::after{
  content: var(--tw-content);
  border-radius: 0px;
}

.checked\:focus\:after\:border-\[0\.125rem\]:focus:checked::after{
  content: var(--tw-content);
  border-width: 0.125rem;
}

.checked\:focus\:after\:border-l-0:focus:checked::after{
  content: var(--tw-content);
  border-left-width: 0px;
}

.checked\:focus\:after\:border-t-0:focus:checked::after{
  content: var(--tw-content);
  border-top-width: 0px;
}

.checked\:focus\:after\:border-solid:focus:checked::after{
  content: var(--tw-content);
  border-style: solid;
}

.checked\:focus\:after\:border-white:focus:checked::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.checked\:focus\:after\:bg-transparent:focus:checked::after{
  content: var(--tw-content);
  background-color: transparent;
}

.active\:bg-\[\#c4d4ef\]:active{
  --tw-bg-opacity: 1;
  background-color: rgb(196 212 239 / var(--tw-bg-opacity, 1));
}

.active\:bg-\[\#cacfd1\]:active{
  --tw-bg-opacity: 1;
  background-color: rgb(202 207 209 / var(--tw-bg-opacity, 1));
}

.active\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.3\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.2\)\]:active{
  --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.3),0 4px 18px 0 rgba(59,113,202,0.2);
  --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.disabled\:text-slate-300:disabled{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.disabled\:hover\:bg-transparent:hover:disabled{
  background-color: transparent;
}

.group\/x:hover .group-hover\/x\:h-\[11px\]{
  height: 11px;
}

.group\/y:hover .group-hover\/y\:w-\[11px\]{
  width: 11px;
}

.group\/x:hover .group-hover\/x\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y:hover .group-hover\/y\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/ps:hover .group-hover\/ps\:opacity-60{
  opacity: 0.6;
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group\/x:focus .group-focus\/x\:h-\[0\.6875rem\]{
  height: 0.6875rem;
}

.group\/y:focus .group-focus\/y\:w-\[0\.6875rem\]{
  width: 0.6875rem;
}

.group\/x:focus .group-focus\/x\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y:focus .group-focus\/y\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/ps:focus .group-focus\/ps\:opacity-100{
  opacity: 1;
}

.group\/ps:focus .group-focus\/ps\:opacity-60{
  opacity: 0.6;
}

.group\/ps:active .group-active\/ps\:opacity-100{
  opacity: 1;
}

.group\/ps.ps--active-x .group-\[\&\.ps--active-x\]\/ps\:block{
  display: block;
}

.group\/ps.ps--active-y .group-\[\&\.ps--active-y\]\/ps\:block{
  display: block;
}

.group\/x.ps--clicking .group-\[\&\.ps--clicking\]\/x\:h-\[11px\]{
  height: 11px;
}

.group\/y.ps--clicking .group-\[\&\.ps--clicking\]\/y\:w-\[11px\]{
  width: 11px;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border{
  border-width: 1px;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border-solid{
  border-style: solid;
}

.group[data-te-datepicker-cell-current] .group-\[\[data-te-datepicker-cell-current\]\]\:border-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.group\/ps.ps--active-x .group-\[\&\.ps--active-x\]\/ps\:bg-transparent{
  background-color: transparent;
}

.group\/ps.ps--active-y .group-\[\&\.ps--active-y\]\/ps\:bg-transparent{
  background-color: transparent;
}

.group\/x.ps--clicking .group-\[\&\.ps--clicking\]\/x\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group\/y.ps--clicking .group-\[\&\.ps--clicking\]\/y\:bg-\[\#999\]{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.group:not([data-te-datepicker-cell-disabled]):not([data-te-datepicker-cell-selected]):hover .group-\[\:not\(\[data-te-datepicker-cell-disabled\]\)\:not\(\[data-te-datepicker-cell-selected\]\)\:hover\]\:bg-neutral-300{
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity, 1));
}

.group:not([data-te-datepicker-cell-selected])[data-te-datepicker-cell-focused] .group-\[\:not\(\[data-te-datepicker-cell-selected\]\)\[data-te-datepicker-cell-focused\]\]\:bg-neutral-100{
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.group[data-te-datepicker-cell-selected] .group-\[\[data-te-datepicker-cell-selected\]\]\:text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group\/ps.ps--scrolling-x .group-\[\&\.ps--scrolling-x\]\/ps\:opacity-60{
  opacity: 0.6;
}

.group\/ps.ps--scrolling-y .group-\[\&\.ps--scrolling-y\]\/ps\:opacity-60{
  opacity: 0.6;
}

.peer:focus ~ .peer-focus\:-translate-y-\[0\.75rem\]{
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:-translate-y-\[0\.9rem\]{
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:-translate-y-\[1\.15rem\]{
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:scale-\[0\.8\]{
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.data-\[te-datepicker-cell-disabled\]\:pointer-events-none[data-te-datepicker-cell-disabled]{
  pointer-events: none;
}

.data-\[te-active\]\:-top-\[38px\][data-te-active]{
  top: -38px;
}

.data-\[te-carousel-fade\]\:z-0[data-te-carousel-fade]{
  z-index: 0;
}

.data-\[te-carousel-fade\]\:z-\[1\][data-te-carousel-fade]{
  z-index: 1;
}

.data-\[te-input-state-active\]\:block[data-te-input-state-active]{
  display: block;
}

.data-\[popper-reference-hidden\]\:hidden[data-popper-reference-hidden]{
  display: none;
}

.data-\[te-input-state-active\]\:-translate-y-\[0\.75rem\][data-te-input-state-active]{
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:-translate-y-\[0\.9rem\][data-te-input-state-active]{
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:-translate-y-\[1\.15rem\][data-te-input-state-active]{
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-active\]\:scale-100[data-te-active]{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-input-state-active\]\:scale-\[0\.8\][data-te-input-state-active]{
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-select-open\]\:scale-100[data-te-select-open]{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[te-autocomplete-state-open\]\:scale-y-100[data-te-autocomplete-state-open]{
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[data-te-autocomplete-option-disabled\]\:cursor-default[data-data-te-autocomplete-option-disabled]{
  cursor: default;
}

.data-\[te-datepicker-cell-disabled\]\:cursor-default[data-te-datepicker-cell-disabled]{
  cursor: default;
}

.data-\[te-input-disabled\]\:cursor-default[data-te-input-disabled]{
  cursor: default;
}

.data-\[te-select-option-disabled\]\:cursor-default[data-te-select-option-disabled]{
  cursor: default;
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:cursor-default[data-te-select-option-disabled][data-te-select-selected]{
  cursor: default;
}

.data-\[te-autocomplete-item-active\]\:bg-black\/5[data-te-autocomplete-item-active]{
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-input-disabled\]\:bg-\[\#e9ecef\][data-te-input-disabled]{
  --tw-bg-opacity: 1;
  background-color: rgb(233 236 239 / var(--tw-bg-opacity, 1));
}

.data-\[te-input-multiple-active\]\:bg-black\/5[data-te-input-multiple-active]{
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-input-state-active\]\:bg-black\/5[data-te-input-state-active]{
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-select-option-selected\]\:bg-black\/\[0\.02\][data-te-select-option-selected]{
  background-color: rgb(0 0 0 / 0.02);
}

.data-\[te-select-option-selected\]\:data-\[te-input-state-active\]\:bg-black\/5[data-te-input-state-active][data-te-select-option-selected]{
  background-color: rgb(0 0 0 / 0.05);
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:bg-transparent[data-te-select-option-disabled][data-te-select-selected]{
  background-color: transparent;
}

.data-\[data-te-autocomplete-option-disabled\]\:text-gray-400[data-data-te-autocomplete-option-disabled]{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-datepicker-cell-disabled\]\:text-neutral-300[data-te-datepicker-cell-disabled]{
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity, 1));
}

.data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled]{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-select-selected\]\:data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled][data-te-select-selected]{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.data-\[te-autocomplete-state-open\]\:opacity-100[data-te-autocomplete-state-open]{
  opacity: 1;
}

.data-\[te-carousel-fade\]\:opacity-0[data-te-carousel-fade]{
  opacity: 0;
}

.data-\[te-carousel-fade\]\:opacity-100[data-te-carousel-fade]{
  opacity: 1;
}

.data-\[te-select-open\]\:opacity-100[data-te-select-open]{
  opacity: 1;
}

.data-\[te-carousel-fade\]\:duration-\[600ms\][data-te-carousel-fade]{
  transition-duration: 600ms;
}

.data-\[te-input-state-active\]\:placeholder\:opacity-100[data-te-input-state-active]::-moz-placeholder{
  opacity: 1;
}

.data-\[te-input-state-active\]\:placeholder\:opacity-100[data-te-input-state-active]::placeholder{
  opacity: 1;
}

.data-\[te-datepicker-cell-disabled\]\:hover\:cursor-default:hover[data-te-datepicker-cell-disabled]{
  cursor: default;
}

.group\/validation[data-te-was-validated] .group-data-\[te-was-validated\]\/validation\:mb-4{
  margin-bottom: 1rem;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-x-0{
  border-left-width: 0px;
  border-right-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-x-0{
  border-left-width: 0px;
  border-right-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-l-0{
  border-left-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-r-0{
  border-right-width: 0px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-t{
  border-top-width: 1px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-l-0{
  border-left-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-r-0{
  border-right-width: 0px;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-t{
  border-top-width: 1px;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-solid{
  border-style: solid;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-solid{
  border-style: solid;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-\[\#14a44d\]{
  --tw-border-opacity: 1;
  border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-\[\#dc4c64\]{
  --tw-border-opacity: 1;
  border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:border-t-transparent{
  border-top-color: transparent;
}

.group[data-te-input-state-active] .group-data-\[te-input-state-active\]\:border-t-transparent{
  border-top-color: transparent;
}

.group\/opt[data-te-select-option-group-ref] .group-data-\[te-select-option-group-ref\]\/opt\:pl-7{
  padding-left: 1.75rem;
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#14a44d\2c _0_1px_0_0_\#14a44d\2c _0_-1px_0_0_\#14a44d\]{
  --tw-shadow: -1px 0 0 #14a44d, 0 1px 0 0 #14a44d, 0 -1px 0 0 #14a44d;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#3b71ca\2c _0_1px_0_0_\#3b71ca\2c _0_-1px_0_0_\#3b71ca\]{
  --tw-shadow: -1px 0 0 #3b71ca, 0 1px 0 0 #3b71ca, 0 -1px 0 0 #3b71ca;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#dc4c64\2c _0_1px_0_0_\#dc4c64\2c _0_-1px_0_0_\#dc4c64\]{
  --tw-shadow: -1px 0 0 #dc4c64, 0 1px 0 0 #dc4c64, 0 -1px 0 0 #dc4c64;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[-1px_0_0_\#ffffff\2c _0_1px_0_0_\#ffffff\2c _0_-1px_0_0_\#ffffff\]{
  --tw-shadow: -1px 0 0 #ffffff, 0 1px 0 0 #ffffff, 0 -1px 0 0 #ffffff;
  --tw-shadow-colored: -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#14a44d\]{
  --tw-shadow: 0 1px 0 0 #14a44d;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#3b71ca\]{
  --tw-shadow: 0 1px 0 0 #3b71ca;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#dc4c64\]{
  --tw-shadow: 0 1px 0 0 #dc4c64;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[0_1px_0_0_\#ffffff\]{
  --tw-shadow: 0 1px 0 0 #ffffff;
  --tw-shadow-colored: 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#14a44d\2c _0_-1px_0_0_\#14a44d\2c _0_1px_0_0_\#14a44d\]{
  --tw-shadow: 1px 0 0 #14a44d, 0 -1px 0 0 #14a44d, 0 1px 0 0 #14a44d;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#3b71ca\2c _0_-1px_0_0_\#3b71ca\2c _0_1px_0_0_\#3b71ca\]{
  --tw-shadow: 1px 0 0 #3b71ca, 0 -1px 0 0 #3b71ca, 0 1px 0 0 #3b71ca;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#dc4c64\2c _0_-1px_0_0_\#dc4c64\2c _0_1px_0_0_\#dc4c64\]{
  --tw-shadow: 1px 0 0 #dc4c64, 0 -1px 0 0 #dc4c64, 0 1px 0 0 #dc4c64;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[data-te-input-focused] .group-data-\[te-input-focused\]\:shadow-\[1px_0_0_\#ffffff\2c _0_-1px_0_0_\#ffffff\2c _0_1px_0_0_\#ffffff\]{
  --tw-shadow: 1px 0 0 #ffffff, 0 -1px 0 0 #ffffff, 0 1px 0 0 #ffffff;
  --tw-shadow-colored: 1px 0 0 var(--tw-shadow-color), 0 -1px 0 0 var(--tw-shadow-color), 0 1px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/validation[data-te-was-validated] .peer:valid ~ .group-data-\[te-was-validated\]\/validation\:peer-valid\:block{
  display: block;
}

.group\/validation[data-te-was-validated] .peer:valid ~ .group-data-\[te-was-validated\]\/validation\:peer-valid\:text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.group\/validation[data-te-was-validated] .peer:invalid ~ .group-data-\[te-was-validated\]\/validation\:peer-invalid\:block{
  display: block;
}

.group\/validation[data-te-was-validated] .peer:invalid ~ .group-data-\[te-was-validated\]\/validation\:peer-invalid\:text-\[rgb\(220\2c 76\2c 100\)\]{
  --tw-text-opacity: 1;
  color: rgb(220 76 100 / var(--tw-text-opacity, 1));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[0\.75rem\]{
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[0\.9rem\]{
  --tw-translate-y: -0.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:-translate-y-\[1\.15rem\]{
  --tw-translate-y: -1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-state-active] ~ .peer-data-\[te-input-state-active\]\:scale-\[0\.8\]{
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer[data-te-input-focused] ~ .peer-data-\[te-input-focused\]\:\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

@media (prefers-reduced-motion: reduce){
  .motion-reduce\:transform-none{
    transform: none;
  }

  @keyframes spin{
    to{
      transform: rotate(360deg);
    }
  }

  .motion-reduce\:animate-\[spin_1\.5s_linear_infinite\]{
    animation: spin 1.5s linear infinite;
  }

  .motion-reduce\:animate-\[spinner-grow_1\.5s_linear_infinite\]{
    animation: spinner-grow 1.5s linear infinite;
  }

  .motion-reduce\:animate-none{
    animation: none;
  }

  .motion-reduce\:transition-none{
    transition-property: none;
  }
}

@media not all and (min-width: 640px){
  .max-sm\:\!hidden{
    display: none !important;
  }

  .max-sm\:p-0{
    padding: 0px;
  }

  .max-sm\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }
}

@media (min-width: 640px){
  .sm\:ml-2{
    margin-left: 0.5rem;
  }

  .sm\:inline{
    display: inline;
  }

  .sm\:max-h-svh{
    max-height: 100svh;
  }

  .sm\:w-1\/2{
    width: 50%;
  }

  .sm\:w-20{
    width: 5rem;
  }

  .sm\:w-auto{
    width: auto;
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:whitespace-nowrap{
    white-space: nowrap;
  }

  .sm\:\!px-3{
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  .sm\:px-1{
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .sm\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 1008px){
  .md\:order-none{
    order: 0;
  }

  .md\:mx-3{
    margin-left: 0.75rem;
    margin-right: 0.75rem;
  }

  .md\:my-0{
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:mr-4{
    margin-right: 1rem;
  }

  .md\:flex{
    display: flex;
  }

  .md\:w-1\/2{
    width: 50%;
  }

  .md\:w-1\/3{
    width: 33.333333%;
  }

  .md\:w-10\/12{
    width: 83.333333%;
  }

  .md\:w-2\/12{
    width: 16.666667%;
  }

  .md\:w-2\/3{
    width: 66.666667%;
  }

  .md\:w-2\/6{
    width: 33.333333%;
  }

  .md\:w-3\/12{
    width: 25%;
  }

  .md\:w-4\/12{
    width: 33.333333%;
  }

  .md\:w-4\/6{
    width: 66.666667%;
  }

  .md\:w-5\/12{
    width: 41.666667%;
  }

  .md\:w-6\/12{
    width: 50%;
  }

  .md\:w-8\/12{
    width: 66.666667%;
  }

  .md\:w-9\/12{
    width: 75%;
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:min-w-80{
    min-width: 20rem;
  }

  .md\:max-w-\[400px\]{
    max-width: 400px;
  }

  .md\:max-w-\[500px\]{
    max-width: 500px;
  }

  .md\:max-w-\[800px\]{
    max-width: 800px;
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-center{
    align-items: center;
  }

  .md\:justify-normal{
    justify-content: normal;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:\!gap-3{
    gap: 0.75rem !important;
  }

  .md\:pe-1{
    padding-inline-end: 0.25rem;
  }

  .md\:pr-1{
    padding-right: 0.25rem;
  }

  .md\:pr-\[17px\]{
    padding-right: 17px;
  }

  .md\:ps-1{
    padding-inline-start: 0.25rem;
  }
}

@media (min-width: 1600px){
  .lg\:w-1\/3{
    width: 33.333333%;
  }

  .lg\:w-3\/6{
    width: 50%;
  }

  .lg\:w-4\/6{
    width: 66.666667%;
  }

  .lg\:w-6\/12{
    width: 50%;
  }

  .lg\:w-full{
    width: 100%;
  }
}

@media (min-width: 320px){
  @media (max-width: 825px){
    @media (orientation: landscape){
      .min-\[320px\]\:max-\[825px\]\:landscape\:h-auto{
        height: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-h-\[305px\]{
        min-height: 305px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-h-\[auto\]{
        min-height: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:min-w-\[auto\]{
        min-width: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:\!flex-row{
        flex-direction: row !important;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:flex-col{
        flex-direction: column;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:\!justify-around{
        justify-content: space-around !important;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:overflow-y-auto{
        overflow-y: auto;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-lg{
        border-radius: 0.5rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-bl-lg{
        border-bottom-left-radius: 0.5rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-bl-none{
        border-bottom-left-radius: 0px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:rounded-tr-none{
        border-top-right-radius: 0px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:p-\[10px\]{
        padding: 10px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:pr-\[10px\]{
        padding-right: 10px;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:text-5xl{
        font-size: 3rem;
        line-height: 1;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:text-\[3rem\]{
        font-size: 3rem;
      }

      .min-\[320px\]\:max-\[825px\]\:landscape\:font-normal{
        font-weight: 400;
      }
    }
  }
}

.rtl\:\!left-auto:where([dir="rtl"], [dir="rtl"] *){
  left: auto !important;
}

.rtl\:\!origin-\[50\%_50\%_0\]:where([dir="rtl"], [dir="rtl"] *){
  transform-origin: 50% 50% 0 !important;
}

.rtl\:\[direction\:rtl\]:where([dir="rtl"], [dir="rtl"] *){
  direction: rtl;
}

@media (prefers-color-scheme: dark){
  .dark\:border-0{
    border-width: 0px;
  }

  .dark\:border-\[\#14a44d\]{
    --tw-border-opacity: 1;
    border-color: rgb(20 164 77 / var(--tw-border-opacity, 1));
  }

  .dark\:border-\[\#4f4f4f\]{
    --tw-border-opacity: 1;
    border-color: rgb(79 79 79 / var(--tw-border-opacity, 1));
  }

  .dark\:border-\[\#dc4c64\]{
    --tw-border-opacity: 1;
    border-color: rgb(220 76 100 / var(--tw-border-opacity, 1));
  }

  .dark\:border-neutral-400{
    --tw-border-opacity: 1;
    border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
  }

  .dark\:border-neutral-500{
    --tw-border-opacity: 1;
    border-color: rgb(115 115 115 / var(--tw-border-opacity, 1));
  }

  .dark\:border-neutral-600{
    --tw-border-opacity: 1;
    border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));
  }

  .dark\:\!bg-neutral-600{
    --tw-bg-opacity: 1 !important;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:bg-\[\#4f4f4f\]{
    --tw-bg-opacity: 1;
    background-color: rgb(79 79 79 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-600{
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-700{
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-neutral-800{
    --tw-bg-opacity: 1;
    background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-transparent{
    background-color: transparent;
  }

  .dark\:bg-zinc-500{
    --tw-bg-opacity: 1;
    background-color: rgb(113 113 122 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-zinc-600\/50{
    background-color: rgb(82 82 91 / 0.5);
  }

  .dark\:bg-zinc-700{
    --tw-bg-opacity: 1;
    background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-zinc-800{
    --tw-bg-opacity: 1;
    background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
  }

  .dark\:fill-gray-400{
    fill: #9ca3af;
  }

  .dark\:text-gray-200{
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-300{
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-200{
    --tw-text-opacity: 1;
    color: rgb(229 229 229 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-300{
    --tw-text-opacity: 1;
    color: rgb(212 212 212 / var(--tw-text-opacity, 1));
  }

  .dark\:text-neutral-400{
    --tw-text-opacity: 1;
    color: rgb(163 163 163 / var(--tw-text-opacity, 1));
  }

  .dark\:text-white{
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:shadow-\[0_4px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.5\)\]{
    --tw-shadow: 0 4px 9px -4px rgba(59,113,202,0.5);
    --tw-shadow-colored: 0 4px 9px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:placeholder\:text-gray-200::-moz-placeholder{
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder\:text-gray-200::placeholder{
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:\!bg-\[\#555\]:hover{
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:hover\:bg-neutral-500:hover{
    --tw-bg-opacity: 1;
    background-color: rgb(115 115 115 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-neutral-600:hover{
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 82 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-neutral-700:hover{
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-white\/10:hover{
    background-color: rgb(255 255 255 / 0.1);
  }

  .dark\:hover\:fill-gray-100:hover{
    fill: #f3f4f6;
  }

  .dark\:hover\:text-\[\#3b71ca\]:hover{
    --tw-text-opacity: 1;
    color: rgb(59 113 202 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:hover{
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:focus\:\!bg-\[\#555\]:focus{
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:focus\:bg-white\/10:focus{
    background-color: rgb(255 255 255 / 0.1);
  }

  .dark\:focus\:text-\[\#3b71ca\]:focus{
    --tw-text-opacity: 1;
    color: rgb(59 113 202 / var(--tw-text-opacity, 1));
  }

  .dark\:focus\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:focus{
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:focus\:before\:shadow-\[0px_0px_0px_13px_rgba\(255\2c 255\2c 255\2c 0\.4\)\]:focus::before{
    content: var(--tw-content);
    --tw-shadow: 0px 0px 0px 13px rgba(255,255,255,0.4);
    --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:checked\:focus\:before\:shadow-\[0px_0px_0px_13px_\#3b71ca\]:focus:checked::before{
    content: var(--tw-content);
    --tw-shadow: 0px 0px 0px 13px #3b71ca;
    --tw-shadow-colored: 0px 0px 0px 13px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:active\:shadow-\[0_8px_9px_-4px_rgba\(59\2c 113\2c 202\2c 0\.2\)\2c 0_4px_18px_0_rgba\(59\2c 113\2c 202\2c 0\.1\)\]:active{
    --tw-shadow: 0 8px 9px -4px rgba(59,113,202,0.2),0 4px 18px 0 rgba(59,113,202,0.1);
    --tw-shadow-colored: 0 8px 9px -4px var(--tw-shadow-color), 0 4px 18px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .dark\:disabled\:text-neutral-600:disabled{
    --tw-text-opacity: 1;
    color: rgb(82 82 82 / var(--tw-text-opacity, 1));
  }

  .dark\:disabled\:hover\:bg-transparent:hover:disabled{
    background-color: transparent;
  }

  .group[data-te-datepicker-cell-current] .dark\:group-\[\[data-te-datepicker-cell-current\]\]\:border-white{
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
  }

  .group:not([data-te-datepicker-cell-disabled]):not([data-te-datepicker-cell-selected]):hover .dark\:group-\[\:not\(\[data-te-datepicker-cell-disabled\]\)\:not\(\[data-te-datepicker-cell-selected\]\)\:hover\]\:bg-white\/10{
    background-color: rgb(255 255 255 / 0.1);
  }

  .group:not([data-te-datepicker-cell-selected])[data-te-datepicker-cell-focused] .dark\:group-\[\:not\(\[data-te-datepicker-cell-selected\]\)\[data-te-datepicker-cell-focused\]\]\:bg-white\/10{
    background-color: rgb(255 255 255 / 0.1);
  }

  .group[data-te-datepicker-cell-disabled] .dark\:group-\[\[data-te-datepicker-cell-disabled\]\]\:text-neutral-500{
    --tw-text-opacity: 1;
    color: rgb(115 115 115 / var(--tw-text-opacity, 1));
  }

  .peer:focus ~ .dark\:peer-focus\:text-gray-200{
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:data-\[te-autocomplete-item-active\]\:bg-white\/30[data-te-autocomplete-item-active]{
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-buttons-timepicker\]\:bg-zinc-700[data-te-buttons-timepicker]{
    --tw-bg-opacity: 1;
    background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
  }

  .dark\:data-\[te-input-disabled\]\:bg-zinc-600[data-te-input-disabled]{
    --tw-bg-opacity: 1;
    background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
  }

  .dark\:data-\[te-input-multiple-active\]\:bg-white\/30[data-te-input-multiple-active]{
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-input-state-active\]\:bg-white\/30[data-te-input-state-active]{
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-select-option-selected\]\:data-\[te-input-state-active\]\:bg-white\/30[data-te-input-state-active][data-te-select-option-selected]{
    background-color: rgb(255 255 255 / 0.3);
  }

  .dark\:data-\[te-select-option-disabled\]\:text-gray-400[data-te-select-option-disabled]{
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }
}

.\[\&\.ps--clicking\]\:\!bg-\[\#eee\].ps--clicking{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1)) !important;
}

.\[\&\.ps--clicking\]\:\!opacity-90.ps--clicking{
  opacity: 0.9 !important;
}

@media (prefers-color-scheme: dark){
  .dark\:\[\&\.ps--clicking\]\:\!bg-\[\#555\].ps--clicking{
    --tw-bg-opacity: 1 !important;
    background-color: rgb(85 85 85 / var(--tw-bg-opacity, 1)) !important;
  }
}

.\[\&\:\:-webkit-scrollbar-button\]\:block::-webkit-scrollbar-button{
  display: block;
}

.\[\&\:\:-webkit-scrollbar-button\]\:h-0::-webkit-scrollbar-button{
  height: 0px;
}

.\[\&\:\:-webkit-scrollbar-button\]\:bg-transparent::-webkit-scrollbar-button{
  background-color: transparent;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:h-\[50px\]::-webkit-scrollbar-thumb{
  height: 50px;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:rounded::-webkit-scrollbar-thumb{
  border-radius: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:bg-\[\#999\]::-webkit-scrollbar-thumb{
  --tw-bg-opacity: 1;
  background-color: rgb(153 153 153 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:rounded-none::-webkit-scrollbar-track-piece{
  border-radius: 0px;
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:rounded-l::-webkit-scrollbar-track-piece{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar-track-piece\]\:bg-transparent::-webkit-scrollbar-track-piece{
  background-color: transparent;
}

.\[\&\:\:-webkit-scrollbar\]\:h-1::-webkit-scrollbar{
  height: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar\]\:w-1::-webkit-scrollbar{
  width: 0.25rem;
}

.hover\:\[\&\:not\(\[data-te-autocomplete-option-disabled\]\)\]\:bg-black\/5:not([data-te-autocomplete-option-disabled]):hover{
  background-color: rgb(0 0 0 / 0.05);
}

@media (prefers-color-scheme: dark){
  .dark\:hover\:\[\&\:not\(\[data-te-autocomplete-option-disabled\]\)\]\:bg-white\/30:not([data-te-autocomplete-option-disabled]):hover{
    background-color: rgb(255 255 255 / 0.3);
  }
}

.\[\&\:not\(\[data-te-input-placeholder-active\]\)\]\:placeholder\:opacity-0:not([data-te-input-placeholder-active])::-moz-placeholder{
  opacity: 0;
}

.\[\&\:not\(\[data-te-input-placeholder-active\]\)\]\:placeholder\:opacity-0:not([data-te-input-placeholder-active])::placeholder{
  opacity: 0;
}

.hover\:\[\&\:not\(\[data-te-select-option-disabled\]\)\]\:bg-black\/5:not([data-te-select-option-disabled]):hover{
  background-color: rgb(0 0 0 / 0.05);
}

@media (prefers-color-scheme: dark){
  .dark\:hover\:\[\&\:not\(\[data-te-select-option-disabled\]\)\]\:bg-white\/30:not([data-te-select-option-disabled]):hover{
    background-color: rgb(255 255 255 / 0.3);
  }
}

.\[\&\:nth-child\(odd\)\]\:bg-neutral-50:nth-child(odd){
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

@media (prefers-color-scheme: dark){
  .\[\&\:nth-child\(odd\)\]\:dark\:bg-neutral-700:nth-child(odd){
    --tw-bg-opacity: 1;
    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));
  }
}

.\[\&\>svg\]\:pointer-events-none>svg{
  pointer-events: none;
}

.\[\&\>svg\]\:mx-auto>svg{
  margin-left: auto;
  margin-right: auto;
}

.\[\&\>svg\]\:h-4>svg{
  height: 1rem;
}

.\[\&\>svg\]\:h-5>svg{
  height: 1.25rem;
}

.\[\&\>svg\]\:h-6>svg{
  height: 1.5rem;
}

.\[\&\>svg\]\:w-4>svg{
  width: 1rem;
}

.\[\&\>svg\]\:w-5>svg{
  width: 1.25rem;
}

.\[\&\>svg\]\:w-6>svg{
  width: 1.5rem;
}

.\[\&\>svg\]\:rotate-180>svg{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:fill-neutral-500>svg{
  fill: #737373;
}

@media (prefers-color-scheme: dark){
  .dark\:\[\&\>svg\]\:fill-white>svg{
    fill: #fff;
  }
}

.\[\&_\.rz-button-box\]\:text-black .rz-button-box{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

@media not all and (min-width: 640px){
  .max-sm\:\[\&_\.rz-button-text\]\:hidden .rz-button-text{
    display: none;
  }

  .max-sm\:\[\&_\.rz-button\]\:\!px-0 .rz-button{
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
}

